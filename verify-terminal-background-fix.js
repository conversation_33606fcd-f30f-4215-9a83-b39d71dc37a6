// 验证终端背景修复的脚本
// 在浏览器开发者工具控制台中运行

console.log('🔍 开始验证终端背景修复...');

// 1. 检查SSH终端组件是否存在
function checkSSHTerminals() {
    const sshTerminals = document.querySelectorAll('[data-ssh-connect-id]');
    console.log(`✅ 找到 ${sshTerminals.length} 个SSH终端组件`);
    return sshTerminals;
}

// 2. 检查本地终端组件是否存在
function checkLocalTerminals() {
    const localTerminals = document.querySelectorAll('.terminal-container:not([data-ssh-connect-id])');
    console.log(`✅ 找到 ${localTerminals.length} 个本地终端组件`);
    return localTerminals;
}

// 3. 检查事件总线
function checkEventBus() {
    if (window.eventBus) {
        console.log('✅ 事件总线可用');
        return window.eventBus;
    } else {
        console.log('❌ 事件总线不可用');
        return null;
    }
}

// 4. 测试背景设置功能
function testBackgroundSettings(eventBus) {
    if (!eventBus) {
        console.log('❌ 无法测试，事件总线不可用');
        return;
    }

    console.log('🧪 开始测试背景设置...');

    // 测试1: 纯色背景
    console.log('📝 测试1: 设置红色背景');
    eventBus.emit('updateTerminalBackground', {
        type: 'color',
        color: '#ff4444',
        imageUrl: '',
        opacity: 1.0
    });

    setTimeout(() => {
        console.log('📝 测试2: 设置蓝色背景');
        eventBus.emit('updateTerminalBackground', {
            type: 'color',
            color: '#4444ff',
            imageUrl: '',
            opacity: 0.8
        });
    }, 2000);

    setTimeout(() => {
        console.log('📝 测试3: 设置图片背景');
        eventBus.emit('updateTerminalBackground', {
            type: 'image',
            color: '#1a1a1a',
            imageUrl: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CiAgPGRlZnM+CiAgICA8bGluZWFyR3JhZGllbnQgaWQ9ImdyYWQiIHgxPSIwJSIgeTE9IjAlIiB4Mj0iMTAwJSIgeTI9IjEwMCUiPgogICAgICA8c3RvcCBvZmZzZXQ9IjAlIiBzdG9wLWNvbG9yPSIjMWU0MDc3Ii8+CiAgICAgIDxzdG9wIG9mZnNldD0iNTAlIiBzdG9wLWNvbG9yPSIjMzc1M2JkIi8+CiAgICAgIDxzdG9wIG9mZnNldD0iMTAwJSIgc3RvcC1jb2xvcj0iIzFkNGVkOCIvPgogICAgPC9saW5lYXJHcmFkaWVudD4KICA8L2RlZnM+CiAgPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSJ1cmwoI2dyYWQpIi8+Cjwvc3ZnPg==',
            opacity: 1.0
        });
    }, 4000);

    setTimeout(() => {
        console.log('📝 测试4: 恢复默认背景');
        eventBus.emit('updateTerminalBackground', {
            type: 'default',
            color: '#1a1a1a',
            imageUrl: '',
            opacity: 1.0
        });
    }, 6000);
}

// 5. 检查终端样式应用情况
function checkTerminalStyles() {
    console.log('🎨 检查终端样式应用情况...');
    
    const allTerminals = [
        ...document.querySelectorAll('[data-ssh-connect-id]'),
        ...document.querySelectorAll('.terminal-container:not([data-ssh-connect-id])')
    ];

    allTerminals.forEach((terminal, index) => {
        const styles = window.getComputedStyle(terminal);
        console.log(`终端 ${index + 1}:`);
        console.log(`  类型: ${terminal.hasAttribute('data-ssh-connect-id') ? 'SSH' : '本地'}`);
        console.log(`  背景色: ${styles.backgroundColor}`);
        console.log(`  背景图片: ${styles.backgroundImage}`);
        console.log(`  透明度: ${styles.opacity}`);
        
        // 检查xterm元素
        const xtermElement = terminal.querySelector('.xterm');
        if (xtermElement) {
            const xtermStyles = window.getComputedStyle(xtermElement);
            console.log(`  xterm背景: ${xtermStyles.backgroundColor}`);
        }
        console.log('---');
    });
}

// 6. 检查配置存储
function checkConfigStorage() {
    console.log('💾 检查配置存储...');
    
    const request = indexedDB.open('ChatermDB', 1);
    
    request.onsuccess = function(event) {
        const db = event.target.result;
        const transaction = db.transaction(['userConfig'], 'readonly');
        const store = transaction.objectStore('userConfig');
        const getRequest = store.get('userConfig');
        
        getRequest.onsuccess = function() {
            const config = getRequest.result;
            if (config && config.terminalBackground) {
                console.log('✅ 终端背景配置已保存:', config.terminalBackground);
            } else {
                console.log('❌ 未找到终端背景配置');
            }
        };
        
        getRequest.onerror = function() {
            console.log('❌ 读取配置失败');
        };
    };
    
    request.onerror = function() {
        console.log('❌ 无法打开数据库');
    };
}

// 7. 主验证函数
function verifyTerminalBackgroundFix() {
    console.log('🚀 开始验证终端背景修复...\n');
    
    // 检查组件
    const sshTerminals = checkSSHTerminals();
    const localTerminals = checkLocalTerminals();
    
    if (sshTerminals.length === 0 && localTerminals.length === 0) {
        console.log('❌ 未找到任何终端组件，请确保已打开终端');
        return;
    }
    
    // 检查事件总线
    const eventBus = checkEventBus();
    
    // 检查当前样式
    checkTerminalStyles();
    
    // 检查配置存储
    checkConfigStorage();
    
    // 测试背景设置
    if (eventBus) {
        console.log('\n🧪 将在3秒后开始自动测试背景设置...');
        setTimeout(() => {
            testBackgroundSettings(eventBus);
        }, 3000);
        
        // 定期检查样式变化
        let checkCount = 0;
        const styleChecker = setInterval(() => {
            checkCount++;
            console.log(`\n📊 第${checkCount}次样式检查:`);
            checkTerminalStyles();
            
            if (checkCount >= 5) {
                clearInterval(styleChecker);
                console.log('\n✅ 验证完成！');
            }
        }, 2000);
    }
}

// 8. 手动测试函数
window.manualTestBg = {
    red: () => {
        const eventBus = checkEventBus();
        if (eventBus) {
            eventBus.emit('updateTerminalBackground', {
                type: 'color',
                color: '#cc0000',
                imageUrl: '',
                opacity: 1.0
            });
            console.log('🔴 设置红色背景');
        }
    },
    green: () => {
        const eventBus = checkEventBus();
        if (eventBus) {
            eventBus.emit('updateTerminalBackground', {
                type: 'color',
                color: '#00cc00',
                imageUrl: '',
                opacity: 1.0
            });
            console.log('🟢 设置绿色背景');
        }
    },
    blue: () => {
        const eventBus = checkEventBus();
        if (eventBus) {
            eventBus.emit('updateTerminalBackground', {
                type: 'color',
                color: '#0066cc',
                imageUrl: '',
                opacity: 1.0
            });
            console.log('🔵 设置蓝色背景');
        }
    },
    default: () => {
        const eventBus = checkEventBus();
        if (eventBus) {
            eventBus.emit('updateTerminalBackground', {
                type: 'default',
                color: '#1a1a1a',
                imageUrl: '',
                opacity: 1.0
            });
            console.log('⚫ 恢复默认背景');
        }
    },
    check: () => {
        checkTerminalStyles();
    }
};

// 启动验证
verifyTerminalBackgroundFix();

console.log('\n📋 可用的手动测试命令:');
console.log('manualTestBg.red() - 红色背景');
console.log('manualTestBg.green() - 绿色背景');
console.log('manualTestBg.blue() - 蓝色背景');
console.log('manualTestBg.default() - 默认背景');
console.log('manualTestBg.check() - 检查当前样式');
