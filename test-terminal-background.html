<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>终端背景测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 6px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .terminal-mock {
            width: 100%;
            height: 200px;
            border: 1px solid #333;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            padding: 10px;
            color: #e2e8f0;
            background: #1a1a1a;
            overflow: auto;
        }
        .controls {
            margin-bottom: 15px;
        }
        .controls label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }
        .controls select, .controls input {
            margin-left: 10px;
            padding: 5px;
        }
        .color-picker {
            width: 50px;
            height: 30px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .opacity-slider {
            width: 200px;
        }
        .button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #40a9ff;
        }
        .status {
            margin-top: 10px;
            padding: 10px;
            background: #f0f0f0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>终端背景设置测试</h1>
        <p>这个页面用于测试终端背景设置功能是否正常工作。</p>

        <div class="test-section">
            <h3>1. 默认背景测试</h3>
            <div class="controls">
                <button class="button" onclick="testDefaultBackground()">应用默认背景</button>
            </div>
            <div class="terminal-mock" id="terminal1">
                $ ls -la<br>
                total 24<br>
                drwxr-xr-x  5 <USER> <GROUP> 4096 Dec  8 10:30 .<br>
                drwxr-xr-x  3 <USER> <GROUP> 4096 Dec  8 10:29 ..<br>
                -rw-r--r--  1 <USER> <GROUP>  220 Dec  8 10:29 .bashrc<br>
                -rw-r--r--  1 <USER> <GROUP>  807 Dec  8 10:29 .profile<br>
                drwxr-xr-x  2 <USER> <GROUP> 4096 Dec  8 10:30 Documents<br>
                $ _
            </div>
        </div>

        <div class="test-section">
            <h3>2. 纯色背景测试</h3>
            <div class="controls">
                <label>背景颜色:</label>
                <input type="color" id="colorPicker" value="#2d3748" class="color-picker" onchange="testColorBackground()">
                <label style="margin-left: 20px;">透明度:</label>
                <input type="range" id="opacitySlider" min="0.1" max="1" step="0.1" value="1" class="opacity-slider" onchange="testColorBackground()">
                <span id="opacityValue">100%</span>
            </div>
            <div class="terminal-mock" id="terminal2">
                $ ps aux | grep node<br>
                user     1234  0.5  2.1 123456 87654 ?        Sl   10:30   0:01 node server.js<br>
                user     5678  0.0  0.1   6789  1234 pts/0    S+   10:32   0:00 grep --color=auto node<br>
                $ _
            </div>
        </div>

        <div class="test-section">
            <h3>3. 图片背景测试</h3>
            <div class="controls">
                <label>图片URL:</label>
                <input type="text" id="imageUrl" placeholder="输入图片URL或选择预设" style="width: 300px;">
                <button class="button" onclick="testImageBackground()">应用图片背景</button>
            </div>
            <div class="controls">
                <button class="button" onclick="usePresetImage('starry')">星空背景</button>
                <button class="button" onclick="usePresetImage('gradient')">渐变背景</button>
                <button class="button" onclick="usePresetImage('grid')">网格背景</button>
            </div>
            <div class="terminal-mock" id="terminal3">
                $ docker ps<br>
                CONTAINER ID   IMAGE          COMMAND                  CREATED         STATUS         PORTS                    NAMES<br>
                abc123def456   nginx:latest   "/docker-entrypoint.…"   2 hours ago     Up 2 hours     0.0.0.0:80->80/tcp      web-server<br>
                def456ghi789   redis:alpine   "docker-entrypoint.s…"   2 hours ago     Up 2 hours     0.0.0.0:6379->6379/tcp  redis-cache<br>
                $ _
            </div>
        </div>

        <div class="test-section">
            <h3>测试状态</h3>
            <div class="status" id="status">
                等待测试...
            </div>
        </div>
    </div>

    <script>
        // 预设图片背景
        const presetImages = {
            starry: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CiAgPGRlZnM+CiAgICA8cmFkaWFsR3JhZGllbnQgaWQ9InN0YXIiIGN4PSI1MCUiIGN5PSI1MCUiIHI9IjUwJSI+CiAgICAgIDxzdG9wIG9mZnNldD0iMCUiIHN0b3AtY29sb3I9IiNmZmZmZmYiIHN0b3Atb3BhY2l0eT0iMC44Ii8+CiAgICAgIDxzdG9wIG9mZnNldD0iMTAwJSIgc3RvcC1jb2xvcj0iIzAwMDAwMCIgc3RvcC1vcGFjaXR5PSIwIi8+CiAgICA8L3JhZGlhbEdyYWRpZW50PgogIDwvZGVmcz4KICA8cmVjdCB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIGZpbGw9IiMwYjE0MjYiLz4KICA8Y2lyY2xlIGN4PSI4IiBjeT0iOCIgcj0iMSIgZmlsbD0idXJsKCNzdGFyKSIvPgogIDxjaXJjbGUgY3g9IjI0IiBjeT0iMTIiIHI9IjEuNSIgZmlsbD0idXJsKCNzdGFyKSIvPgogIDxjaXJjbGUgY3g9IjMyIiBjeT0iMjgiIHI9IjEiIGZpbGw9InVybCgjc3RhcikiLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjMyIiByPSIwLjUiIGZpbGw9InVybCgjc3RhcikiLz4KPC9zdmc+',
            gradient: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CiAgPGRlZnM+CiAgICA8bGluZWFyR3JhZGllbnQgaWQ9ImdyYWQiIHgxPSIwJSIgeTE9IjAlIiB4Mj0iMTAwJSIgeTI9IjEwMCUiPgogICAgICA8c3RvcCBvZmZzZXQ9IjAlIiBzdG9wLWNvbG9yPSIjMWU0MDc3Ii8+CiAgICAgIDxzdG9wIG9mZnNldD0iNTAlIiBzdG9wLWNvbG9yPSIjMzc1M2JkIi8+CiAgICAgIDxzdG9wIG9mZnNldD0iMTAwJSIgc3RvcC1jb2xvcj0iIzFkNGVkOCIvPgogICAgPC9saW5lYXJHcmFkaWVudD4KICA8L2RlZnM+CiAgPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSJ1cmwoI2dyYWQpIi8+Cjwvc3ZnPg==',
            grid: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CiAgPGRlZnM+CiAgICA8cGF0dGVybiBpZD0iZ2VvIiB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHBhdHRlcm5Vbml0cz0idXNlclNwYWNlT25Vc2UiPgogICAgICA8cG9seWdvbiBwb2ludHM9IjEwLDAgMjAsMTAgMTAsMjAgMCwxMCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjNGY0NjU5IiBzdHJva2Utd2lkdGg9IjEiLz4KICAgIDwvcGF0dGVybj4KICA8L2RlZnM+CiAgPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjMWExYTFhIi8+CiAgPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSJ1cmwoI2dlbykiLz4KPC9zdmc+'
        };

        function updateStatus(message) {
            document.getElementById('status').textContent = new Date().toLocaleTimeString() + ': ' + message;
        }

        function testDefaultBackground() {
            const terminal = document.getElementById('terminal1');
            terminal.style.background = '#1a1a1a';
            terminal.style.backgroundImage = 'none';
            terminal.style.opacity = '1';
            updateStatus('应用默认背景 - 深色主题背景色');
        }

        function testColorBackground() {
            const color = document.getElementById('colorPicker').value;
            const opacity = document.getElementById('opacitySlider').value;
            const terminal = document.getElementById('terminal2');
            
            terminal.style.backgroundColor = color;
            terminal.style.backgroundImage = 'none';
            terminal.style.opacity = opacity;
            
            document.getElementById('opacityValue').textContent = Math.round(opacity * 100) + '%';
            updateStatus(`应用纯色背景 - 颜色: ${color}, 透明度: ${Math.round(opacity * 100)}%`);
        }

        function testImageBackground() {
            const imageUrl = document.getElementById('imageUrl').value;
            const terminal = document.getElementById('terminal3');
            
            if (imageUrl) {
                terminal.style.backgroundImage = `url(${imageUrl})`;
                terminal.style.backgroundSize = 'cover';
                terminal.style.backgroundPosition = 'center';
                terminal.style.backgroundRepeat = 'no-repeat';
                terminal.style.backgroundColor = 'transparent';
                updateStatus(`应用图片背景 - URL: ${imageUrl.substring(0, 50)}...`);
            } else {
                updateStatus('请输入图片URL');
            }
        }

        function usePresetImage(type) {
            const imageUrl = presetImages[type];
            document.getElementById('imageUrl').value = imageUrl;
            testImageBackground();
            updateStatus(`应用预设${type}背景`);
        }

        // 初始化透明度显示
        document.getElementById('opacitySlider').addEventListener('input', function() {
            document.getElementById('opacityValue').textContent = Math.round(this.value * 100) + '%';
        });

        // 页面加载完成后的初始状态
        window.onload = function() {
            updateStatus('页面加载完成，可以开始测试终端背景功能');
        };
    </script>
</body>
</html>
