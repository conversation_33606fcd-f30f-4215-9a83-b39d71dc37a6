// 终端背景设置调试脚本
// 在浏览器控制台中运行此脚本来测试背景设置功能

console.log('=== 终端背景设置调试脚本 ===');

// 1. 检查事件总线是否存在
function checkEventBus() {
    console.log('\n1. 检查事件总线...');
    
    if (typeof window !== 'undefined' && window.eventBus) {
        console.log('✅ 全局事件总线存在');
        return window.eventBus;
    }
    
    // 尝试从Vue应用中获取
    const app = document.querySelector('#app').__vue_app__;
    if (app && app.config.globalProperties.$eventBus) {
        console.log('✅ Vue应用事件总线存在');
        return app.config.globalProperties.$eventBus;
    }
    
    console.log('❌ 未找到事件总线');
    return null;
}

// 2. 检查终端组件是否存在
function checkTerminalComponents() {
    console.log('\n2. 检查终端组件...');
    
    const sshTerminals = document.querySelectorAll('[data-ssh-connect-id]');
    const localTerminals = document.querySelectorAll('.terminal-container');
    
    console.log(`找到 ${sshTerminals.length} 个SSH终端组件`);
    console.log(`找到 ${localTerminals.length} 个本地终端组件`);
    
    return {
        sshTerminals: Array.from(sshTerminals),
        localTerminals: Array.from(localTerminals)
    };
}

// 3. 检查用户配置存储
function checkUserConfig() {
    console.log('\n3. 检查用户配置...');
    
    // 检查IndexedDB中的配置
    const request = indexedDB.open('ChatermDB', 1);
    
    request.onsuccess = function(event) {
        const db = event.target.result;
        const transaction = db.transaction(['userConfig'], 'readonly');
        const store = transaction.objectStore('userConfig');
        const getRequest = store.get('userConfig');
        
        getRequest.onsuccess = function() {
            const config = getRequest.result;
            if (config && config.terminalBackground) {
                console.log('✅ 找到终端背景配置:', config.terminalBackground);
            } else {
                console.log('❌ 未找到终端背景配置');
            }
        };
        
        getRequest.onerror = function() {
            console.log('❌ 读取配置失败');
        };
    };
    
    request.onerror = function() {
        console.log('❌ 无法打开数据库');
    };
}

// 4. 测试背景设置事件
function testBackgroundEvents(eventBus) {
    console.log('\n4. 测试背景设置事件...');
    
    if (!eventBus) {
        console.log('❌ 无法测试，事件总线不存在');
        return;
    }
    
    // 测试纯色背景
    console.log('发送纯色背景事件...');
    eventBus.emit('updateTerminalBackground', {
        type: 'color',
        color: '#2d3748',
        imageUrl: '',
        opacity: 1.0
    });
    
    setTimeout(() => {
        // 测试图片背景
        console.log('发送图片背景事件...');
        eventBus.emit('updateTerminalBackground', {
            type: 'image',
            color: '#1a1a1a',
            imageUrl: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CiAgPGRlZnM+CiAgICA8bGluZWFyR3JhZGllbnQgaWQ9ImdyYWQiIHgxPSIwJSIgeTE9IjAlIiB4Mj0iMTAwJSIgeTI9IjEwMCUiPgogICAgICA8c3RvcCBvZmZzZXQ9IjAlIiBzdG9wLWNvbG9yPSIjMWU0MDc3Ii8+CiAgICAgIDxzdG9wIG9mZnNldD0iNTAlIiBzdG9wLWNvbG9yPSIjMzc1M2JkIi8+CiAgICAgIDxzdG9wIG9mZnNldD0iMTAwJSIgc3RvcC1jb2xvcj0iIzFkNGVkOCIvPgogICAgPC9saW5lYXJHcmFkaWVudD4KICA8L2RlZnM+CiAgPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSJ1cmwoI2dyYWQpIi8+Cjwvc3ZnPg==',
            opacity: 0.8
        });
    }, 2000);
    
    setTimeout(() => {
        // 恢复默认背景
        console.log('发送默认背景事件...');
        eventBus.emit('updateTerminalBackground', {
            type: 'default',
            color: '#1a1a1a',
            imageUrl: '',
            opacity: 1.0
        });
    }, 4000);
}

// 5. 检查终端样式
function checkTerminalStyles() {
    console.log('\n5. 检查终端样式...');
    
    const terminals = document.querySelectorAll('.terminal-container, [data-ssh-connect-id]');
    
    terminals.forEach((terminal, index) => {
        const styles = window.getComputedStyle(terminal);
        console.log(`终端 ${index + 1}:`);
        console.log(`  背景色: ${styles.backgroundColor}`);
        console.log(`  背景图片: ${styles.backgroundImage}`);
        console.log(`  透明度: ${styles.opacity}`);
        
        // 检查xterm元素
        const xtermElement = terminal.querySelector('.xterm');
        if (xtermElement) {
            const xtermStyles = window.getComputedStyle(xtermElement);
            console.log(`  xterm背景色: ${xtermStyles.backgroundColor}`);
        }
    });
}

// 6. 主要调试函数
function debugTerminalBackground() {
    console.log('开始调试终端背景设置...');
    
    const eventBus = checkEventBus();
    const components = checkTerminalComponents();
    
    checkUserConfig();
    checkTerminalStyles();
    
    if (eventBus) {
        console.log('\n准备测试背景事件（将在2秒后开始）...');
        setTimeout(() => {
            testBackgroundEvents(eventBus);
        }, 2000);
    }
    
    // 设置样式检查定时器
    setInterval(() => {
        console.log('\n=== 定期样式检查 ===');
        checkTerminalStyles();
    }, 10000);
}

// 7. 手动设置背景的辅助函数
window.setTerminalBackground = function(type, color, imageUrl, opacity) {
    console.log(`手动设置终端背景: ${type}`);
    
    const eventBus = checkEventBus();
    if (eventBus) {
        eventBus.emit('updateTerminalBackground', {
            type: type || 'color',
            color: color || '#2d3748',
            imageUrl: imageUrl || '',
            opacity: opacity || 1.0
        });
    } else {
        console.log('❌ 无法设置背景，事件总线不存在');
    }
};

// 8. 快捷测试函数
window.testTerminalBg = {
    red: () => window.setTerminalBackground('color', '#8b0000'),
    green: () => window.setTerminalBackground('color', '#006400'),
    blue: () => window.setTerminalBackground('color', '#000080'),
    purple: () => window.setTerminalBackground('color', '#4b0082'),
    gradient: () => window.setTerminalBackground('image', '', 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CiAgPGRlZnM+CiAgICA8bGluZWFyR3JhZGllbnQgaWQ9ImdyYWQiIHgxPSIwJSIgeTE9IjAlIiB4Mj0iMTAwJSIgeTI9IjEwMCUiPgogICAgICA8c3RvcCBvZmZzZXQ9IjAlIiBzdG9wLWNvbG9yPSIjMWU0MDc3Ii8+CiAgICAgIDxzdG9wIG9mZnNldD0iNTAlIiBzdG9wLWNvbG9yPSIjMzc1M2JkIi8+CiAgICAgIDxzdG9wIG9mZnNldD0iMTAwJSIgc3RvcC1jb2xvcj0iIzFkNGVkOCIvPgogICAgPC9saW5lYXJHcmFkaWVudD4KICA8L2RlZnM+CiAgPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSJ1cmwoI2dyYWQpIi8+Cjwvc3ZnPg=='),
    default: () => window.setTerminalBackground('default')
};

// 启动调试
debugTerminalBackground();

console.log('\n=== 调试脚本加载完成 ===');
console.log('可用的测试命令:');
console.log('- testTerminalBg.red() - 红色背景');
console.log('- testTerminalBg.green() - 绿色背景');
console.log('- testTerminalBg.blue() - 蓝色背景');
console.log('- testTerminalBg.purple() - 紫色背景');
console.log('- testTerminalBg.gradient() - 渐变背景');
console.log('- testTerminalBg.default() - 默认背景');
console.log('- setTerminalBackground(type, color, imageUrl, opacity) - 自定义背景');
