<template>
  <div class="userInfo">
    <a-card
      :bordered="false"
      class="userInfo-container"
    >
      <a-form
        :colon="false"
        label-align="left"
        wrapper-align="right"
        :label-col="{ span: 7, offset: 0 }"
        :wrapper-col="{ span: 17, class: 'right-aligned-wrapper' }"
        class="custom-form"
      >
        <a-form-item>
          <template #label>
            <span class="label-text">{{ $t('user.baseSetting') }}</span>
          </template>
        </a-form-item>
        <a-form-item
          label="外观模式"
          class="user_my-ant-form-item"
        >
          <a-radio-group
            v-model:value="userConfig.theme"
            class="custom-radio-group"
            @change="changeTheme"
          >
            <a-radio value="dark">深色</a-radio>
            <a-radio value="light">浅色</a-radio>
            <a-radio value="auto">自动</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item
          :label="$t('user.language')"
          class="user_my-ant-form-item"
        >
          <a-radio-group
            v-model:value="userConfig.language"
            class="custom-radio-group"
            @change="changeLanguage"
          >
            <a-radio value="zh-CN">简体中文</a-radio>
            <a-radio value="en-US">English</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item
          :label="$t('user.watermark')"
          class="user_my-ant-form-item"
        >
          <a-radio-group
            v-model:value="userConfig.watermark"
            class="custom-radio-group"
          >
            <a-radio value="open">{{ $t('user.watermarkOpen') }}</a-radio>
            <a-radio value="close">{{ $t('user.watermarkClose') }}</a-radio>
          </a-radio-group>
        </a-form-item>

        <!-- 主题颜色设置 -->
        <a-form-item
          label="主题颜色"
          class="user_my-ant-form-item"
        >
          <div class="theme-container">
            <!-- 主题预设 -->
            <div class="theme-presets-section">
              <h4 class="section-title">主题预设</h4>

              <!-- 深色主题 -->
              <div class="theme-category">
                <h5 class="category-title">深色主题</h5>
                <div class="theme-presets-grid">
                  <div
                    v-for="theme in darkThemes"
                    :key="theme.name"
                    class="theme-preset-item"
                    :class="{ active: isThemeActive(theme) }"
                    @click="applyThemePreset(theme)"
                  >
                    <div class="theme-preview">
                      <div class="preview-colors">
                        <div
                          class="color-dot"
                          :style="{ backgroundColor: theme.colors.primaryColor }"
                        ></div>
                        <div
                          class="color-dot"
                          :style="{ backgroundColor: theme.colors.accentColor }"
                        ></div>
                        <div
                          class="color-dot"
                          :style="{ backgroundColor: theme.colors.backgroundColor }"
                        ></div>
                      </div>
                    </div>
                    <span class="theme-name">{{ theme.name }}</span>
                  </div>
                </div>
              </div>

              <!-- 浅色主题 -->
              <div class="theme-category">
                <h5 class="category-title">浅色主题</h5>
                <div class="theme-presets-grid">
                  <div
                    v-for="theme in lightThemes"
                    :key="theme.name"
                    class="theme-preset-item"
                    :class="{ active: isThemeActive(theme) }"
                    @click="applyThemePreset(theme)"
                  >
                    <div class="theme-preview">
                      <div class="preview-colors">
                        <div
                          class="color-dot"
                          :style="{ backgroundColor: theme.colors.primaryColor }"
                        ></div>
                        <div
                          class="color-dot"
                          :style="{ backgroundColor: theme.colors.accentColor }"
                        ></div>
                        <div
                          class="color-dot"
                          :style="{ backgroundColor: theme.colors.backgroundColor }"
                        ></div>
                      </div>
                    </div>
                    <span class="theme-name">{{ theme.name }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 主题操作按钮 -->
            <div class="theme-actions">
              <a-button
                @click="resetThemeColors"
                size="small"
              >
                重置为默认主题
              </a-button>
            </div>
          </div>
        </a-form-item>
      </a-form>
    </a-card>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, onBeforeUnmount } from 'vue'
import { notification } from 'ant-design-vue'
import { userConfigStore } from '@/services/userConfigStoreService'
import eventBus from '@/utils/eventBus'
import { getActualTheme, addSystemThemeListener } from '@/utils/themeUtils'
import { useI18n } from 'vue-i18n'

const api = window.api
const { locale, t } = useI18n()

const userConfig = ref({
  language: 'zh-CN',
  watermark: 'open',
  theme: 'auto',
  themeColors: {
    primaryColor: '#1890ff',
    accentColor: '#52c41a',
    backgroundColor: '#141414',
    surfaceColor: '#1f1f1f',
    textColor: '#ffffff',
    borderColor: '#303030'
  }
})

// 深色主题预设
const darkThemes = ref([
  {
    name: '经典深色',
    colors: {
      primaryColor: '#1890ff',
      accentColor: '#52c41a',
      backgroundColor: '#141414',
      surfaceColor: '#1f1f1f',
      textColor: '#ffffff',
      borderColor: '#303030'
    }
  },
  {
    name: 'VS Code 深色',
    colors: {
      primaryColor: '#007acc',
      accentColor: '#4fc3f7',
      backgroundColor: '#1e1e1e',
      surfaceColor: '#252526',
      textColor: '#cccccc',
      borderColor: '#3c3c3c'
    }
  },
  {
    name: 'GitHub 深色',
    colors: {
      primaryColor: '#58a6ff',
      accentColor: '#7c3aed',
      backgroundColor: '#0d1117',
      surfaceColor: '#161b22',
      textColor: '#f0f6fc',
      borderColor: '#30363d'
    }
  },
  {
    name: '赛博朋克',
    colors: {
      primaryColor: '#00ffff',
      accentColor: '#ff00ff',
      backgroundColor: '#0a0a0a',
      surfaceColor: '#1a0a1a',
      textColor: '#00ffff',
      borderColor: '#ff00ff'
    }
  },
  {
    name: '深海蓝',
    colors: {
      primaryColor: '#0088ff',
      accentColor: '#00ddff',
      backgroundColor: '#0c1618',
      surfaceColor: '#162329',
      textColor: '#e0f7ff',
      borderColor: '#2a4a5a'
    }
  },
  {
    name: '森林绿',
    colors: {
      primaryColor: '#00cc66',
      accentColor: '#66ff99',
      backgroundColor: '#0f1b0c',
      surfaceColor: '#1a2e17',
      textColor: '#e0ffe0',
      borderColor: '#2a4a2a'
    }
  },
  {
    name: '紫罗兰',
    colors: {
      primaryColor: '#8b5cf6',
      accentColor: '#a78bfa',
      backgroundColor: '#1a0d2e',
      surfaceColor: '#2d1b4e',
      textColor: '#f3e8ff',
      borderColor: '#4c1d95'
    }
  },
  {
    name: '日落橙',
    colors: {
      primaryColor: '#ff6b35',
      accentColor: '#ffaa44',
      backgroundColor: '#2d1b0f',
      surfaceColor: '#4a2c1a',
      textColor: '#fff4e6',
      borderColor: '#8b4513'
    }
  },
  {
    name: '极光绿',
    colors: {
      primaryColor: '#00ff88',
      accentColor: '#88ffdd',
      backgroundColor: '#0a1a0f',
      surfaceColor: '#1a2e1f',
      textColor: '#e0fff0',
      borderColor: '#2a5a3a'
    }
  },
  {
    name: '暗夜红',
    colors: {
      primaryColor: '#ff4757',
      accentColor: '#ff6b7a',
      backgroundColor: '#2d1b1b',
      surfaceColor: '#4a2e2e',
      textColor: '#ffe6e6',
      borderColor: '#8b2635'
    }
  },
  {
    name: '星空蓝',
    colors: {
      primaryColor: '#3742fa',
      accentColor: '#5352ed',
      backgroundColor: '#0a0e27',
      surfaceColor: '#1a1e3a',
      textColor: '#e6e8ff',
      borderColor: '#2f3349'
    }
  },
  {
    name: '薄荷绿',
    colors: {
      primaryColor: '#2ed573',
      accentColor: '#7bed9f',
      backgroundColor: '#0f1b14',
      surfaceColor: '#1e2e23',
      textColor: '#e8f5e8',
      borderColor: '#3a5a3a'
    }
  }
])

// 浅色主题预设
const lightThemes = ref([
  {
    name: '经典浅色',
    colors: {
      primaryColor: '#1890ff',
      accentColor: '#52c41a',
      backgroundColor: '#ffffff',
      surfaceColor: '#fafafa',
      textColor: '#262626',
      borderColor: '#d9d9d9'
    }
  },
  {
    name: 'GitHub 浅色',
    colors: {
      primaryColor: '#0969da',
      accentColor: '#1f883d',
      backgroundColor: '#ffffff',
      surfaceColor: '#f6f8fa',
      textColor: '#24292f',
      borderColor: '#d0d7de'
    }
  },
  {
    name: 'VS Code 浅色',
    colors: {
      primaryColor: '#005a9e',
      accentColor: '#0e639c',
      backgroundColor: '#ffffff',
      surfaceColor: '#f3f3f3',
      textColor: '#333333',
      borderColor: '#cccccc'
    }
  },
  {
    name: '天空蓝',
    colors: {
      primaryColor: '#0066cc',
      accentColor: '#1890ff',
      backgroundColor: '#f0f8ff',
      surfaceColor: '#e6f4ff',
      textColor: '#003366',
      borderColor: '#91d5ff'
    }
  },
  {
    name: '春日绿',
    colors: {
      primaryColor: '#389e0d',
      accentColor: '#52c41a',
      backgroundColor: '#f6ffed',
      surfaceColor: '#f0f9e8',
      textColor: '#135200',
      borderColor: '#b7eb8f'
    }
  },
  {
    name: '樱花粉',
    colors: {
      primaryColor: '#eb2f96',
      accentColor: '#f759ab',
      backgroundColor: '#fff0f6',
      surfaceColor: '#ffe6f0',
      textColor: '#780650',
      borderColor: '#ffadd2'
    }
  },
  {
    name: '日出橙',
    colors: {
      primaryColor: '#fa541c',
      accentColor: '#ff7a45',
      backgroundColor: '#fff2e8',
      surfaceColor: '#ffe7d3',
      textColor: '#ad2102',
      borderColor: '#ffbb96'
    }
  },
  {
    name: '薰衣草',
    colors: {
      primaryColor: '#722ed1',
      accentColor: '#9254de',
      backgroundColor: '#f9f0ff',
      surfaceColor: '#f4e6ff',
      textColor: '#391085',
      borderColor: '#d3adf7'
    }
  },
  {
    name: '海洋蓝',
    colors: {
      primaryColor: '#1890ff',
      accentColor: '#40a9ff',
      backgroundColor: '#e6f7ff',
      surfaceColor: '#d6f0ff',
      textColor: '#003a8c',
      borderColor: '#91d5ff'
    }
  },
  {
    name: '柠檬黄',
    colors: {
      primaryColor: '#faad14',
      accentColor: '#ffc53d',
      backgroundColor: '#fffbe6',
      surfaceColor: '#fff7d1',
      textColor: '#ad6800',
      borderColor: '#ffe58f'
    }
  },
  {
    name: '极简灰',
    colors: {
      primaryColor: '#595959',
      accentColor: '#8c8c8c',
      backgroundColor: '#fafafa',
      surfaceColor: '#f5f5f5',
      textColor: '#262626',
      borderColor: '#d9d9d9'
    }
  }
])

// 颜色预设
const colorPresets = [
  { label: '蓝色', colors: ['#1890ff', '#40a9ff', '#69c0ff', '#91d5ff'] },
  { label: '绿色', colors: ['#52c41a', '#73d13d', '#95de64', '#b7eb8f'] },
  { label: '红色', colors: ['#f5222d', '#ff4d4f', '#ff7875', '#ffa39e'] },
  { label: '橙色', colors: ['#fa541c', '#ff7a45', '#ff9c6e', '#ffbb96'] },
  { label: '紫色', colors: ['#722ed1', '#9254de', '#b37feb', '#d3adf7'] },
  { label: '灰色', colors: ['#595959', '#8c8c8c', '#bfbfbf', '#d9d9d9'] }
]

const loadSavedConfig = async (retryCount = 0) => {
  const maxRetries = 3
  const retryDelay = 1000 // 1秒

  try {
    // 等待一小段时间确保数据库服务完全初始化
    if (retryCount === 0) {
      await new Promise((resolve) => setTimeout(resolve, 100))
    }

    const savedConfig = await userConfigStore.getConfig()
    if (savedConfig) {
      userConfig.value = {
        ...userConfig.value,
        ...savedConfig,
        // 确保themeColors字段正确初始化
        themeColors: savedConfig.themeColors || {
          primaryColor: '#1890ff',
          accentColor: '#52c41a',
          backgroundColor: '#141414',
          surfaceColor: '#1f1f1f',
          textColor: '#ffffff',
          borderColor: '#303030'
        }
      }
      const actualTheme = getActualTheme(userConfig.value.theme)
      document.documentElement.className = `theme-${actualTheme}`

      // 安全地调用eventBus.emit，添加空值检查
      if (eventBus && typeof eventBus.emit === 'function') {
        eventBus.emit('updateTheme', actualTheme)
      }

      // 安全地调用api.updateTheme，添加空值检查
      if (api && typeof api.updateTheme === 'function') {
        api.updateTheme(userConfig.value.theme)
      }

      console.log('Config loaded successfully')
    }
  } catch (error) {
    console.error(`Failed to load config (attempt ${retryCount + 1}):`, error)

    // 检查是否是数据库初始化相关的错误
    const isDatabaseError =
      error.message && (error.message.includes('database') || error.message.includes('IndexedDB') || error.message.includes('transaction'))

    // 如果是数据库错误且还有重试次数，等待后重试
    if (isDatabaseError && retryCount < maxRetries) {
      console.log(`Database not ready, retrying config load in ${retryDelay}ms...`)
      setTimeout(() => {
        loadSavedConfig(retryCount + 1)
      }, retryDelay)
      return
    }

    // 如果不是数据库错误或重试次数用完，使用默认配置但不显示错误通知
    if (isDatabaseError && retryCount >= maxRetries) {
      console.warn('Database initialization failed after retries, using default configuration silently')
    } else {
      // 只有在非数据库相关错误时才显示错误通知
      console.error('Config load failed with non-database error')
      notification.error({
        message: t('user.loadConfigFailed'),
        description: t('user.loadConfigFailedDescription')
      })
    }

    // 使用默认配置
    const actualTheme = getActualTheme('auto')
    document.documentElement.className = `theme-${actualTheme}`
    userConfig.value.theme = 'auto'
  }
}

const saveConfig = async () => {
  try {
    const configToStore = {
      language: userConfig.value.language,
      watermark: userConfig.value.watermark,
      theme: userConfig.value.theme
    }
    await userConfigStore.saveConfig(configToStore)

    // 安全地调用eventBus.emit，添加空值检查
    if (eventBus && typeof eventBus.emit === 'function') {
      eventBus.emit('updateWatermark', configToStore.watermark)
      eventBus.emit('updateTheme', configToStore.theme)
    }
  } catch (error) {
    console.error('Failed to save config:', error)
    notification.error({
      message: t('user.error'),
      description: t('user.saveConfigFailedDescription')
    })
  }
}

watch(
  () => userConfig.value,
  async () => {
    await saveConfig()
  },
  { deep: true }
)

let systemThemeListener = null

onMounted(async () => {
  console.log('General component mounted')
  console.log('userConfig initial value:', userConfig.value)
  try {
    await loadSavedConfig()
    console.log('Config loaded successfully, userConfig:', userConfig.value)

    // Add system theme change listener
    setupSystemThemeListener()
  } catch (error) {
    console.error('Error in General component onMounted:', error)
  }
})

onBeforeUnmount(() => {
  eventBus.off('updateTheme')

  // Remove system theme listener
  if (systemThemeListener) {
    systemThemeListener()
    systemThemeListener = null
  }
})

const changeLanguage = async () => {
  locale.value = userConfig.value.language
  localStorage.setItem('lang', userConfig.value.language)
  // 移除configStore调用，因为我们已经通过userConfigStore保存配置

  // 安全地通知其他组件语言已更改，需要刷新数据
  if (eventBus && typeof eventBus.emit === 'function') {
    eventBus.emit('languageChanged', userConfig.value.language)
  }

  await saveConfig()
}

// Setup system theme change listener
const setupSystemThemeListener = () => {
  systemThemeListener = addSystemThemeListener(async (newSystemTheme) => {
    // Only update theme if user has selected 'auto' mode
    if (userConfig.value.theme === 'auto') {
      const actualTheme = getActualTheme(userConfig.value.theme)
      const currentTheme = document.documentElement.className.replace('theme-', '')

      if (currentTheme !== actualTheme) {
        // System theme changed, update application theme
        document.documentElement.className = `theme-${actualTheme}`

        // 安全地调用eventBus.emit，添加空值检查
        if (eventBus && typeof eventBus.emit === 'function') {
          eventBus.emit('updateTheme', actualTheme)
        }

        // 安全地调用api.updateTheme，添加空值检查
        if (api && typeof api.updateTheme === 'function') {
          await api.updateTheme(userConfig.value.theme)
        }
        console.log(`System theme changed to ${newSystemTheme}, updating application theme to ${actualTheme}`)
      }
    }
  })

  // Listen for system theme changes from main process (Windows)
  if (window.api && window.api.onSystemThemeChanged) {
    window.api.onSystemThemeChanged((newSystemTheme) => {
      if (userConfig.value.theme === 'auto') {
        const currentTheme = document.documentElement.className.replace('theme-', '')
        if (currentTheme !== newSystemTheme) {
          document.documentElement.className = `theme-${newSystemTheme}`

          // 安全地调用eventBus.emit，添加空值检查
          if (eventBus && typeof eventBus.emit === 'function') {
            eventBus.emit('updateTheme', newSystemTheme)
          }

          console.log(`System theme changed to ${newSystemTheme} (from main process)`)
        }
      }
    })
  }
}

const changeTheme = async () => {
  try {
    const actualTheme = getActualTheme(userConfig.value.theme)
    document.documentElement.className = `theme-${actualTheme}`

    // 安全地调用eventBus.emit，添加空值检查
    if (eventBus && typeof eventBus.emit === 'function') {
      eventBus.emit('updateTheme', actualTheme)
    }

    // 安全地调用api.updateTheme，添加空值检查
    if (api && typeof api.updateTheme === 'function') {
      await api.updateTheme(userConfig.value.theme)
    }

    await saveConfig()
  } catch (error) {
    console.error('Failed to change theme:', error)
    notification.error({
      message: t('user.themeSwitchFailed'),
      description: t('user.themeSwitchFailedDescription')
    })
  }
}

// 重置主题颜色到默认值
const resetThemeColors = () => {
  userConfig.value.themeColors = {
    primaryColor: '#1890ff',
    accentColor: '#52c41a',
    backgroundColor: '#141414',
    surfaceColor: '#1f1f1f',
    textColor: '#ffffff',
    borderColor: '#303030'
  }
}

// 检查主题是否激活
const isThemeActive = (theme) => {
  const current = userConfig.value.themeColors
  const preset = theme.colors
  return (
    current.primaryColor === preset.primaryColor &&
    current.accentColor === preset.accentColor &&
    current.backgroundColor === preset.backgroundColor &&
    current.surfaceColor === preset.surfaceColor &&
    current.textColor === preset.textColor &&
    current.borderColor === preset.borderColor
  )
}

// 应用主题预设
const applyThemePreset = async (theme) => {
  userConfig.value.themeColors = { ...theme.colors }
  applyThemeColors()
  await saveConfig()
  console.log('Theme preset applied:', theme.name)
}

// 应用主题颜色
const applyThemeColors = () => {
  const colors = userConfig.value.themeColors

  // 应用CSS自定义属性 - 使用应用的主要变量名
  const root = document.documentElement
  root.style.setProperty('--primary-color', colors.primaryColor)
  root.style.setProperty('--accent-color', colors.accentColor)
  root.style.setProperty('--bg-color', colors.backgroundColor)
  root.style.setProperty('--bg-color-secondary', colors.surfaceColor)
  root.style.setProperty('--text-color', colors.textColor)
  root.style.setProperty('--border-color', colors.borderColor)

  // 发送事件通知其他组件
  if (eventBus && typeof eventBus.emit === 'function') {
    eventBus.emit('updateThemeColors', colors)
  }

  notification.success({
    message: '成功',
    description: '主题已应用'
  })
}
</script>

<style scoped>
.userInfo {
  width: 100%;
  height: 100%;
}

.userInfo-container {
  width: 100%;
  height: 100%;
  background-color: var(--bg-color) !important;
  border-radius: 6px;
  overflow: hidden;
  padding: 4px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
  color: var(--text-color);
}

:deep(.ant-card) {
  height: 100%;
  background-color: var(--bg-color) !important;
}

:deep(.ant-card-body) {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--bg-color);
}

.custom-form {
  color: var(--text-color);
  align-content: center;
}

.custom-form :deep(.ant-form-item-label) {
  padding-right: 20px;
}

.custom-form :deep(.ant-form-item-label > label) {
  color: var(--text-color);
}

.custom-form :deep(.ant-input),
.custom-form :deep(.ant-input-number),
.custom-form :deep(.ant-radio-wrapper) {
  color: var(--text-color);
}

.custom-form :deep(.ant-input-number) {
  background-color: var(--input-number-bg);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  transition: all 0.3s;
  width: 100px !important;
}

.custom-form :deep(.ant-input-number:hover),
.custom-form :deep(.ant-input-number:focus),
.custom-form :deep(.ant-input-number-focused) {
  background-color: var(--input-number-hover-bg);
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.custom-form :deep(.ant-input-number-input) {
  height: 32px;
  padding: 4px 8px;
  background-color: transparent;
  color: var(--text-color);
}

.label-text {
  font-size: 20px;
  font-weight: bold;
  line-height: 1.3;
}

.user_my-ant-form-item {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  color: rgba(0, 0, 0, 0.65);
  font-size: 30px;
  font-variant: tabular-nums;
  line-height: 1.5;
  list-style: none;
  -webkit-font-feature-settings: 'tnum';
  font-feature-settings: 'tnum';
  margin-bottom: 14px;
  vertical-align: top;
  color: #ffffff;
}

.divider-container {
  width: calc(65%);
  margin: -10px calc(16%);
}

:deep(.right-aligned-wrapper) {
  text-align: right;
  color: #ffffff;
}

.checkbox-md :deep(.ant-checkbox-inner) {
  width: 20px;
  height: 20px;
}

/* 主题设置样式 */
.theme-container {
  width: 100%;
}

.theme-presets-section,
.custom-colors-section {
  margin-bottom: 20px;
}

.section-title {
  color: var(--text-color);
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 12px;
  margin-top: 0;
}

/* 主题预设网格 */
.theme-presets-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(90px, 1fr));
  gap: 10px;
  margin-bottom: 16px;
  max-height: 300px;
  overflow-y: auto;
}

.theme-preset-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 12px 8px;
  border: 2px solid transparent;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: var(--bg-color-secondary);
}

.theme-preset-item:hover {
  border-color: var(--primary-color, #1890ff);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.theme-preset-item.active {
  border-color: var(--primary-color, #1890ff);
  background-color: rgba(24, 144, 255, 0.1);
}

.theme-preview {
  width: 55px;
  height: 35px;
  border-radius: 6px;
  overflow: hidden;
  border: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-colors {
  display: flex;
  gap: 2px;
}

.color-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.theme-name {
  font-size: 10px;
  color: var(--text-color);
  text-align: center;
  font-weight: 500;
  line-height: 1.2;
}

/* 自定义颜色样式 */
.theme-colors-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.theme-color-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.color-label {
  min-width: 80px;
  color: var(--text-color);
  font-size: 12px;
}

.theme-color-actions {
  display: flex;
  gap: 8px;
  margin-top: 16px;
}
</style>
