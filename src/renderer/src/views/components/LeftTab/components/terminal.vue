<template>
  <div class="userInfo">
    <a-card
      :bordered="false"
      class="userInfo-container"
    >
      <a-form
        :colon="false"
        label-align="left"
        wrapper-align="right"
        :label-col="{ span: 7, offset: 0 }"
        :wrapper-col="{ span: 17, class: 'right-aligned-wrapper' }"
        class="custom-form"
      >
        <a-form-item>
          <template #label>
            <span class="label-text">{{ $t('user.terminalSetting') }}</span>
          </template>
        </a-form-item>

        <!-- 终端类型 -->
        <a-form-item
          :label="$t('user.terminalType')"
          class="user_my-ant-form-item"
        >
          <a-select
            v-model:value="userConfig.terminalType"
            class="terminal-type-select"
          >
            <a-select-option value="xterm">xterm</a-select-option>
            <a-select-option value="xterm-256color">xterm-256color</a-select-option>
            <a-select-option value="vt100">vt100</a-select-option>
            <a-select-option value="vt220">vt220</a-select-option>
          </a-select>
        </a-form-item>

        <!-- 字体大小 -->
        <a-form-item
          :label="$t('user.fontSize')"
          class="user_my-ant-form-item"
        >
          <a-input-number
            v-model:value="userConfig.fontSize"
            :min="8"
            :max="32"
            :step="1"
            class="font-size-input"
          />
        </a-form-item>

        <!-- 字体系列 -->
        <a-form-item
          :label="$t('user.fontFamily')"
          class="user_my-ant-form-item"
        >
          <a-select
            v-model:value="userConfig.fontFamily"
            class="font-family-select"
          >
            <a-select-option
              v-for="font in fontFamilyOptions"
              :key="font.value"
              :value="font.value"
            >
              {{ font.label }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <!-- 滚动缓冲区 -->
        <a-form-item
          :label="$t('user.scrollBack')"
          class="user_my-ant-form-item"
        >
          <a-input-number
            v-model:value="userConfig.scrollBack"
            :min="100"
            :max="10000"
            :step="100"
            class="scrollback-input"
          />
        </a-form-item>

        <!-- 光标样式 -->
        <a-form-item
          :label="$t('user.cursorStyle')"
          class="user_my-ant-form-item"
        >
          <a-radio-group
            v-model:value="userConfig.cursorStyle"
            class="cursor-style-group"
          >
            <a-radio value="block">{{ $t('user.cursorBlock') }}</a-radio>
            <a-radio value="bar">{{ $t('user.cursorBar') }}</a-radio>
            <a-radio value="underline">{{ $t('user.cursorUnderline') }}</a-radio>
          </a-radio-group>
        </a-form-item>

        <!-- 鼠标事件设置 -->
        <a-form-item>
          <template #label>
            <span class="label-text">{{ $t('user.mouseEvents') }}</span>
          </template>
        </a-form-item>

        <!-- 右键事件 -->
        <a-form-item
          :label="$t('user.rightMouseEvent')"
          class="user_my-ant-form-item"
        >
          <a-radio-group
            v-model:value="userConfig.rightMouseEvent"
            class="mouse-event-group"
          >
            <a-radio value="paste">{{ $t('user.paste') }}</a-radio>
            <a-radio value="contextMenu">{{ $t('user.contextMenu') }}</a-radio>
          </a-radio-group>
        </a-form-item>

        <!-- 中键事件 -->
        <a-form-item
          :label="$t('user.middleMouseEvent')"
          class="user_my-ant-form-item"
        >
          <a-radio-group
            v-model:value="userConfig.middleMouseEvent"
            class="mouse-event-group"
          >
            <a-radio value="paste">{{ $t('user.paste') }}</a-radio>
            <a-radio value="contextMenu">{{ $t('user.contextMenu') }}</a-radio>
            <a-radio value="none">{{ $t('user.none') }}</a-radio>
          </a-radio-group>
        </a-form-item>

        <!-- SSH 代理设置 -->
        <a-form-item>
          <template #label>
            <span class="label-text">{{ $t('user.sshSettings') }}</span>
          </template>
        </a-form-item>

        <!-- SSH 代理状态 -->
        <a-form-item
          :label="$t('user.sshAgentsStatus')"
          class="user_my-ant-form-item"
        >
          <a-radio-group
            v-model:value="userConfig.sshAgentsStatus"
            class="ssh-agents-group"
          >
            <a-radio :value="1">{{ $t('user.enabled') }}</a-radio>
            <a-radio :value="2">{{ $t('user.disabled') }}</a-radio>
          </a-radio-group>
        </a-form-item>

        <!-- SSH 代理配置 -->
        <a-form-item
          :label="$t('user.sshAgentConfig')"
          class="user_my-ant-form-item"
        >
          <a-button
            type="primary"
            @click="showAgentConfigModal"
            class="config-button"
          >
            {{ $t('user.configureAgent') }}
          </a-button>
        </a-form-item>

        <!-- 保存按钮 -->
        <a-form-item class="save-button-item">
          <a-button
            type="primary"
            @click="saveTerminalConfig"
            :loading="saving"
            class="save-button"
          >
            {{ $t('user.saveSettings') }}
          </a-button>
        </a-form-item>
      </a-form>
    </a-card>
        <a-form-item
          :label="$t('user.terminalType')"
          class="user_my-ant-form-item"
        >
          <a-select
            v-model:value="userConfig.value.terminalType"
            class="terminal-type-select"
          >
            <a-select-option value="xterm">xterm</a-select-option>
            <a-select-option value="xterm-256color">xterm-256color</a-select-option>
            <a-select-option value="vt100">vt100</a-select-option>
            <a-select-option value="vt102">vt102</a-select-option>
            <a-select-option value="vt220">vt220</a-select-option>
            <a-select-option value="vt320">vt320</a-select-option>
            <a-select-option value="linux">linux</a-select-option>
            <a-select-option value="scoansi">scoansi</a-select-option>
            <a-select-option value="ansi">ansi</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item
          :label="$t('user.fontFamily')"
          class="user_my-ant-form-item"
        >
          <a-select
            v-model:value="userConfig.value.fontFamily"
            class="font-family-select"
            :options="fontFamilyOptions"
          />
        </a-form-item>
        <a-form-item
          :label="$t('user.fontSize')"
          class="user_my-ant-form-item"
        >
          <a-input-number
            v-model:value="userConfig.value.fontSize"
            :bordered="false"
            style="width: 20%"
            :min="8"
            :max="64"
            class="user_my-ant-form-item-content"
          />
        </a-form-item>
        <a-form-item
          :label="$t('user.scrollBack')"
          class="user_my-ant-form-item"
        >
          <a-input-number
            v-model:value="userConfig.value.scrollBack"
            :bordered="false"
            style="width: 20%"
            :min="1"
            class="user_my-ant-form-item-content"
          />
        </a-form-item>
        <a-form-item
          :label="$t('user.cursorStyle')"
          class="user_my-ant-form-item"
        >
          <a-radio-group
            v-model:value="userConfig.value.cursorStyle"
            class="custom-radio-group"
          >
            <a-radio value="block">{{ $t('user.cursorStyleBlock') }}</a-radio>
            <a-radio value="bar">{{ $t('user.cursorStyleBar') }}</a-radio>
            <a-radio value="underline">{{ $t('user.cursorStyleUnderline') }}</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item
          label="SSH Agents"
          class="user_my-ant-form-item"
        >
          <a-switch
            :checked="userConfig.value.sshAgentsStatus === 1"
            class="user_my-ant-form-item-content"
            @change="handleSshAgentsStatusChange"
          />
        </a-form-item>
        <a-form-item
          v-show="userConfig.value.sshAgentsStatus === 1"
          :label="$t('user.sshAgentSettings')"
          class="user_my-ant-form-item"
        >
          <a-button
            class="setting-button"
            @click="openAgentConfig"
            >{{ $t('common.setting') }}</a-button
          >
        </a-form-item>
        <a-form-item
          :label="$t('user.proxySettings')"
          class="user_my-ant-form-item"
        >
          <a-button
            class="setting-button"
            @click="openProxyConfig"
            >{{ $t('common.setting') }}</a-button
          >
        </a-form-item>
        <a-form-item
          :label="$t('user.mouseEvent')"
          class="user_my-ant-form-item"
        >
          <div class="mouse-event-container">
            <div class="mouse-event-row">
              <span class="mouse-event-label">{{ $t('user.middleMouseEvent') }}:</span>
              <a-select
                v-model:value="userConfig.value.middleMouseEvent"
                class="mouse-event-select"
              >
                <a-select-option value="none">{{ $t('user.none') }}</a-select-option>
                <a-select-option value="paste">{{ $t('user.pasteClipboard') }}</a-select-option>
                <a-select-option value="contextMenu">{{ $t('user.showContextMenu') }}</a-select-option>
              </a-select>
            </div>
            <div class="mouse-event-row">
              <span class="mouse-event-label">{{ $t('user.rightMouseEvent') }}:</span>
              <a-select
                v-model:value="userConfig.value.rightMouseEvent"
                class="mouse-event-select"
              >
                <a-select-option value="none">{{ $t('user.none') }}</a-select-option>
                <a-select-option value="paste">{{ $t('user.pasteClipboard') }}</a-select-option>
                <a-select-option value="contextMenu">{{ $t('user.showContextMenu') }}</a-select-option>
              </a-select>
            </div>
          </div>
        </a-form-item>
        <a-form-item
          :label="$t('user.terminalBackground')"
          class="user_my-ant-form-item"
        >
          <div class="terminal-background-container">
            <div class="background-type-row">
              <span class="background-label">{{ $t('user.terminalBackgroundType') }}:</span>
              <a-select
                v-model:value="userConfig.value.terminalBackground.type"
                class="background-type-select"
              >
                <a-select-option value="default">{{ $t('user.terminalBackgroundDefault') }}</a-select-option>
                <a-select-option value="color">{{ $t('user.terminalBackgroundColor') }}</a-select-option>
                <a-select-option value="image">{{ $t('user.terminalBackgroundImage') }}</a-select-option>
              </a-select>
            </div>
            <div
              v-if="userConfig.value.terminalBackground.type === 'color'"
              class="background-color-row"
            >
              <span class="background-label">{{ $t('user.terminalBackgroundColorPicker') }}:</span>
              <input
                v-model="userConfig.value.terminalBackground.color"
                type="color"
                class="color-picker"
              />
            </div>
            <div
              v-if="userConfig.value.terminalBackground.type === 'image'"
              class="background-image-row"
            >
              <span class="background-label">{{ $t('user.terminalBackgroundImageUrl') }}:</span>
              <div class="image-input-container">
                <a-input
                  v-model:value="userConfig.value.terminalBackground.imageUrl"
                  :placeholder="$t('user.terminalBackgroundImageUrlPlaceholder')"
                  class="image-url-input"
                />
                <a-upload
                  :show-upload-list="false"
                  :before-upload="handleImageUpload"
                  accept="image/*"
                >
                  <a-button class="upload-button">
                    {{ $t('user.terminalBackgroundImageUpload') }}
                  </a-button>
                </a-upload>
              </div>
            </div>
            <div
              v-if="userConfig.value.terminalBackground.type === 'image'"
              class="background-presets-container"
            >
              <div class="background-presets-row">
                <span class="background-label">预设背景:</span>
              </div>
              <div class="preset-backgrounds">
                <div
                  v-for="preset in backgroundPresets"
                  :key="preset.name"
                  class="preset-item"
                  :class="{ active: userConfig.value.terminalBackground.imageUrl === preset.url }"
                  @click="selectPresetBackground(preset)"
                >
                  <img
                    :src="preset.thumbnail"
                    :alt="preset.name"
                    class="preset-thumbnail"
                  />
                  <span class="preset-name">{{ preset.name }}</span>
                </div>
              </div>
            </div>
            <div
              v-if="userConfig.value.terminalBackground.type !== 'default'"
              class="background-opacity-row"
            >
              <span class="background-label">{{ $t('user.terminalBackgroundOpacity') }}:</span>
              <a-slider
                v-model:value="userConfig.value.terminalBackground.opacity"
                :min="0.1"
                :max="1"
                :step="0.1"
                class="opacity-slider"
              />
              <span class="opacity-value">{{ Math.round(userConfig.value.terminalBackground.opacity * 100) }}%</span>
            </div>
          </div>
        </a-form-item>

        <!-- 程序外观主题颜色设置 -->
        <a-form-item
          :label="t('user.themeColors')"
          v-if="userConfig.value.themeColors"
        >
          <div class="theme-colors-container">
            <div class="theme-color-row">
              <span class="color-label">{{ t('user.primaryColor') }}:</span>
              <a-color-picker
                v-model:value="userConfig.value.themeColors.primaryColor"
                :presets="colorPresets"
                show-text
              />
            </div>
            <div class="theme-color-row">
              <span class="color-label">{{ t('user.accentColor') }}:</span>
              <a-color-picker
                v-model:value="userConfig.value.themeColors.accentColor"
                :presets="colorPresets"
                show-text
              />
            </div>
            <div class="theme-color-row">
              <span class="color-label">{{ t('user.backgroundColor') }}:</span>
              <a-color-picker
                v-model:value="userConfig.value.themeColors.backgroundColor"
                :presets="colorPresets"
                show-text
              />
            </div>
            <div class="theme-color-row">
              <span class="color-label">{{ t('user.surfaceColor') }}:</span>
              <a-color-picker
                v-model:value="userConfig.value.themeColors.surfaceColor"
                :presets="colorPresets"
                show-text
              />
            </div>
            <div class="theme-color-row">
              <span class="color-label">{{ t('user.textColor') }}:</span>
              <a-color-picker
                v-model:value="userConfig.value.themeColors.textColor"
                :presets="colorPresets"
                show-text
              />
            </div>
            <div class="theme-color-row">
              <span class="color-label">{{ t('user.borderColor') }}:</span>
              <a-color-picker
                v-model:value="userConfig.value.themeColors.borderColor"
                :presets="colorPresets"
                show-text
              />
            </div>
            <div class="theme-color-actions">
              <a-button
                @click="resetThemeColors"
                size="small"
              >
                {{ t('user.resetToDefault') }}
              </a-button>
              <a-button
                @click="applyThemeColors"
                type="primary"
                size="small"
              >
                {{ t('user.applyTheme') }}
              </a-button>
            </div>
          </div>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- SSH 代理配置模态框 -->
    <a-modal
      v-model:open="agentConfigModalVisible"
      :title="$t('user.sshAgentConfig')"
      width="600px"
      @ok="saveAgentConfig"
      @cancel="cancelAgentConfig"
    >
      <a-form
        :colon="false"
        label-align="left"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item :label="$t('user.agentPath')">
          <a-input
            v-model:value="agentConfig.path"
            :placeholder="$t('user.agentPathPlaceholder')"
          />
        </a-form-item>

        <a-form-item :label="$t('user.agentArgs')">
          <a-input
            v-model:value="agentConfig.args"
            :placeholder="$t('user.agentArgsPlaceholder')"
          />
        </a-form-item>

        <a-form-item :label="$t('user.agentEnv')">
          <a-textarea
            v-model:value="agentConfig.env"
            :rows="4"
            :placeholder="$t('user.agentEnvPlaceholder')"
          />
        </a-form-item>
      </a-form>
    </a-modal>
      :title="$t('user.sshAgentSettings')"
      width="700px"
    >
      <a-table
        :row-key="(record) => record.fingerprint"
        :columns="columns"
        :data-source="agentKeys"
        size="small"
        :pagination="false"
        :locale="{ emptyText: $t('user.noKeyAdd') }"
        class="agent-table"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <a-button
              type="link"
              @click="removeKey(record)"
              >{{ $t('user.remove') }}
            </a-button>
          </template>
        </template>
      </a-table>

      <a-form
        layout="inline"
        style="width: 100%; margin-top: 20px; margin-bottom: 10px"
      >
        <a-form-item
          :label="t('personal.key')"
          style="flex: 1"
        >
          <a-select
            v-model:value="keyChainData"
            :options="keyChainOptions"
            :field-names="{ value: 'key', label: 'label' }"
            style="width: 200px"
          />
        </a-form-item>
        <a-form-item>
          <a-button
            type="primary"
            @click="addKey"
            >{{ $t('common.add') }}</a-button
          >
        </a-form-item>
      </a-form>
    </a-modal>

    <a-modal
      v-model:open="sshProxyConfigShowModalVisible"
      :title="$t('user.proxySettings')"
      width="700px"
    >
      <a-table
        :row-key="(record) => record.name"
        :columns="proxyConfigColumns"
        :data-source="userConfig.value.sshProxyConfigs"
        size="small"
        :pagination="false"
        :locale="{ emptyText: $t('user.noProxyAdd') }"
        class="agent-table"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <a-button
              type="link"
              @click="removeProxyConfig(record.name)"
              >{{ $t('user.remove') }}
            </a-button>
          </template>
        </template>
      </a-table>

      <template #footer>
        <a-button
          type="primary"
          @click="handleProxyConfigAdd"
          >{{ $t('common.add') }}</a-button
        >
        <a-button @click="handleProxyConfigClose">{{ $t('common.close') }}</a-button>
      </template>
    </a-modal>

    <a-modal
      v-model:open="sshProxyConfigAddModalVisible"
      :title="$t('user.proxySettings')"
      :ok-text="$t('common.confirm')"
      :cancel-text="$t('common.cancel')"
      @ok="handleAddSshProxyConfigConfirm"
      @cancel="handleAddSshProxyConfigClose"
    >
      <a-form
        ref="proxyForm"
        class="proxy-form"
        :model="proxyConfig"
        :rules="proxyConfigRules"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-form-item
          name="name"
          :label="$t('user.proxyName')"
          style="margin-bottom: 12px"
        >
          <a-input
            v-model:value="proxyConfig.name"
            :placeholder="$t('user.proxyHost')"
          />
        </a-form-item>
        <a-form-item
          name="proxyType"
          :label="$t('user.proxyType')"
          style="margin-bottom: 12px"
        >
          <a-select
            v-model:value="proxyConfig.type"
            class="proxy-form-select"
            :placeholder="$t('user.proxyType')"
          >
            <a-select-option value="HTTP">HTTP</a-select-option>
            <a-select-option value="HTTPS">HTTPS</a-select-option>
            <a-select-option value="SOCKS4">SOCKS4</a-select-option>
            <a-select-option value="SOCKS5">SOCKS5</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item
          name="host"
          :label="$t('user.proxyHost')"
          style="margin-bottom: 12px"
        >
          <a-input
            v-model:value="proxyConfig.host"
            :placeholder="$t('user.proxyHost')"
          />
        </a-form-item>
        <a-form-item
          name="port"
          :label="$t('user.proxyPort')"
          style="margin-bottom: 12px"
        >
          <a-input-number
            v-model:value="proxyConfig.port"
            :min="1"
            :max="65535"
            :placeholder="$t('user.proxyPort')"
            style="width: 100%"
          />
        </a-form-item>
        <a-form-item
          name="enableProxyIdentity"
          :label="$t('user.enableProxyIdentity')"
          style="margin-bottom: 12px"
        >
          <a-switch
            :checked="proxyConfig.enableProxyIdentity"
            class="user_my-ant-form-item-content"
            @click="handleSshProxyIdentityChange"
          />
        </a-form-item>
        <a-form-item
          v-if="proxyConfig.enableProxyIdentity"
          name="proxyUsername"
          :label="$t('user.proxyUsername')"
          style="margin-bottom: 12px"
        >
          <a-input
            v-model:value="proxyConfig.username"
            :placeholder="$t('user.proxyUsername')"
          />
        </a-form-item>
        <a-form-item
          v-if="proxyConfig.enableProxyIdentity"
          name="proxyPassword"
          :label="$t('user.proxyPassword')"
          style="margin-bottom: 12px"
        >
          <a-input-password
            v-model:value="proxyConfig.password"
            :placeholder="$t('user.proxyPassword')"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { notification } from 'ant-design-vue'
import { userConfigStore } from '@/services/userConfigStoreService'
import { useI18n } from 'vue-i18n'
import eventBus from '@/utils/eventBus'

const { t } = useI18n()

// 响应式数据
const userConfig = ref({
  terminalType: 'xterm-256color',
  fontSize: 14,
  fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
  scrollBack: 1000,
  cursorStyle: 'block',
  rightMouseEvent: 'paste',
  middleMouseEvent: 'paste',
  sshAgentsStatus: 1
})

const agentConfigModalVisible = ref(false)
const saving = ref(false)

const agentConfig = reactive({
  path: '',
  args: '',
  env: ''
})

// 字体选项
const fontFamilyOptions = ref([
  { label: 'Monaco', value: 'Monaco, Menlo, "Ubuntu Mono", monospace' },
  { label: 'Consolas', value: 'Consolas, "Courier New", monospace' },
  { label: 'Menlo', value: 'Menlo, Monaco, "Ubuntu Mono", monospace' },
  { label: 'Ubuntu Mono', value: '"Ubuntu Mono", Monaco, Menlo, monospace' },
  { label: 'Source Code Pro', value: '"Source Code Pro", Monaco, Menlo, monospace' },
  { label: 'Fira Code', value: '"Fira Code", Monaco, Menlo, monospace' },
  { label: 'JetBrains Mono', value: '"JetBrains Mono", Monaco, Menlo, monospace' }
])

// 方法
const showAgentConfigModal = () => {
  agentConfigModalVisible.value = true
}

const saveAgentConfig = () => {
  // 保存代理配置逻辑
  try {
    // 这里可以添加保存到配置文件的逻辑
    notification.success({
      message: t('common.success'),
      description: t('user.agentConfigSaved')
    })
    agentConfigModalVisible.value = false
  } catch (error) {
    notification.error({
      message: t('common.error'),
      description: t('user.agentConfigSaveFailed')
    })
  }
}

const cancelAgentConfig = () => {
  agentConfigModalVisible.value = false
}

const saveTerminalConfig = async () => {
  saving.value = true
  try {
    // 保存终端配置
    await userConfigStore.saveConfig(userConfig.value)

    // 发送事件通知其他组件配置已更新
    eventBus.emit('terminalConfigUpdated', userConfig.value)

    notification.success({
      message: t('common.success'),
      description: t('user.terminalConfigSaved')
    })
  } catch (error) {
    notification.error({
      message: t('common.error'),
      description: t('user.terminalConfigSaveFailed')
    })
  } finally {
    saving.value = false
  }
}

// 初始化配置
const loadTerminalConfig = async () => {
  try {
    const config = await userConfigStore.getConfig('terminal')
    if (config) {
      Object.assign(userConfig.value, config)
    }
  } catch (error) {
    console.error('Failed to load terminal config:', error)
  }
}

// 监听配置变化
watch(userConfig, (newConfig) => {
  // 实时应用配置变化
  eventBus.emit('terminalConfigChanged', newConfig)
}, { deep: true })

// 组件挂载时加载配置
onMounted(() => {
  loadTerminalConfig()
})

// 预设背景选项
const backgroundPresets = [
  {
    name: '星空',
    url: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8ZGVmcz4KICAgIDxyYWRpYWxHcmFkaWVudCBpZD0iZ3JhZGllbnQiIGN4PSI1MCUiIGN5PSI1MCUiIHI9IjUwJSI+CiAgICAgIDxzdG9wIG9mZnNldD0iMCUiIHN0b3AtY29sb3I9IiMwYTBhMjMiLz4KICAgICAgPHN0b3Agb2Zmc2V0PSIxMDAlIiBzdG9wLWNvbG9yPSIjMDAwMDAwIi8+CiAgICA8L3JhZGlhbEdyYWRpZW50PgogIDwvZGVmcz4KICA8cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0idXJsKCNncmFkaWVudCkiLz4KICA8Y2lyY2xlIGN4PSIyMCIgY3k9IjMwIiByPSIxIiBmaWxsPSJ3aGl0ZSIgb3BhY2l0eT0iMC44Ii8+CiAgPGNpcmNsZSBjeD0iNzAiIGN5PSIyMCIgcj0iMS41IiBmaWxsPSJ3aGl0ZSIgb3BhY2l0eT0iMC42Ii8+CiAgPGNpcmNsZSBjeD0iNDAiIGN5PSI3MCIgcj0iMC41IiBmaWxsPSJ3aGl0ZSIgb3BhY2l0eT0iMC45Ii8+CiAgPGNpcmNsZSBjeD0iODAiIGN5PSI2MCIgcj0iMSIgZmlsbD0id2hpdGUiIG9wYWNpdHk9IjAuNyIvPgogIDxjaXJjbGUgY3g9IjEwIiBjeT0iODAiIHI9IjAuNSIgZmlsbD0id2hpdGUiIG9wYWNpdHk9IjAuOCIvPgo8L3N2Zz4=',
    thumbnail:
      'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CiAgPGRlZnM+CiAgICA8cmFkaWFsR3JhZGllbnQgaWQ9ImdyYWRpZW50IiBjeD0iNTAlIiBjeT0iNTAlIiByPSI1MCUiPgogICAgICA8c3RvcCBvZmZzZXQ9IjAlIiBzdG9wLWNvbG9yPSIjMGEwYTIzIi8+CiAgICAgIDxzdG9wIG9mZnNldD0iMTAwJSIgc3RvcC1jb2xvcj0iIzAwMDAwMCIvPgogICAgPC9yYWRpYWxHcmFkaWVudD4KICA8L2RlZnM+CiAgPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSJ1cmwoI2dyYWRpZW50KSIvPgogIDxjaXJjbGUgY3g9IjgiIGN5PSIxMiIgcj0iMC41IiBmaWxsPSJ3aGl0ZSIgb3BhY2l0eT0iMC44Ii8+CiAgPGNpcmNsZSBjeD0iMjgiIGN5PSI4IiByPSIwLjUiIGZpbGw9IndoaXRlIiBvcGFjaXR5PSIwLjYiLz4KICA8Y2lyY2xlIGN4PSIxNiIgY3k9IjI4IiByPSIwLjUiIGZpbGw9IndoaXRlIiBvcGFjaXR5PSIwLjkiLz4KICA8Y2lyY2xlIGN4PSIzMiIgY3k9IjI0IiByPSIwLjUiIGZpbGw9IndoaXRlIiBvcGFjaXR5PSIwLjciLz4KPC9zdmc+'
  },
  {
    name: '渐变蓝',
    url: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8ZGVmcz4KICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZ3JhZGllbnQiIHgxPSIwJSIgeTE9IjAlIiB4Mj0iMTAwJSIgeTI9IjEwMCUiPgogICAgICA8c3RvcCBvZmZzZXQ9IjAlIiBzdG9wLWNvbG9yPSIjMWUzYThhIi8+CiAgICAgIDxzdG9wIG9mZnNldD0iMTAwJSIgc3RvcC1jb2xvcj0iIzFhMWEyZiIvPgogICAgPC9saW5lYXJHcmFkaWVudD4KICA8L2RlZnM+CiAgPHJlY3Qgd2lkdGg9IjEwMCIgaGVpZ2h0PSIxMDAiIGZpbGw9InVybCgjZ3JhZGllbnQpIi8+Cjwvc3ZnPg==',
    thumbnail:
      'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CiAgPGRlZnM+CiAgICA8bGluZWFyR3JhZGllbnQgaWQ9ImdyYWRpZW50IiB4MT0iMCUiIHkxPSIwJSIgeDI9IjEwMCUiIHkyPSIxMDAlIj4KICAgICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iIzFlM2E4YSIvPgogICAgICA8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0b3AtY29sb3I9IiMxYTFhMmYiLz4KICAgIDwvbGluZWFyR3JhZGllbnQ+CiAgPC9kZWZzPgogIDxyZWN0IHdpZHRoPSI0MCIgaGVpZ2h0PSI0MCIgZmlsbD0idXJsKCNncmFkaWVudCkiLz4KPC9zdmc+'
  },
  {
    name: '深色网格',
    url: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8ZGVmcz4KICAgIDxwYXR0ZXJuIGlkPSJncmlkIiB3aWR0aD0iMTAiIGhlaWdodD0iMTAiIHBhdHRlcm5Vbml0cz0idXNlclNwYWNlT25Vc2UiPgogICAgICA8cGF0aCBkPSJNIDEwIDAgTCAwIDAgMCAxMCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjMzMzIiBzdHJva2Utd2lkdGg9IjAuNSIvPgogICAgPC9wYXR0ZXJuPgogIDwvZGVmcz4KICA8cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iIzFhMWExYSIvPgogIDxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSJ1cmwoI2dyaWQpIi8+Cjwvc3ZnPg==',
    thumbnail:
      'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CiAgPGRlZnM+CiAgICA8cGF0dGVybiBpZD0iZ3JpZCIgd2lkdGg9IjQiIGhlaWdodD0iNCIgcGF0dGVyblVuaXRzPSJ1c2VyU3BhY2VPblVzZSI+CiAgICAgIDxwYXRoIGQ9Ik0gNCAwIEwgMCAwIDAgNCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjMzMzIiBzdHJva2Utd2lkdGg9IjAuNSIvPgogICAgPC9wYXR0ZXJuPgogIDwvZGVmcz4KICA8cmVjdCB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIGZpbGw9IiMxYTFhMWEiLz4KICA8cmVjdCB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIGZpbGw9InVybCgjZ3JpZCkiLz4KPC9zdmc+'
  },
  {
    name: '森林绿',
    url: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8ZGVmcz4KICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZm9yZXN0IiB4MT0iMCUiIHkxPSIwJSIgeDI9IjEwMCUiIHkyPSIxMDAlIj4KICAgICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iIzFlMzkyYSIvPgogICAgICA8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0b3AtY29sb3I9IiMwZjFmMTUiLz4KICAgIDwvbGluZWFyR3JhZGllbnQ+CiAgPC9kZWZzPgogIDxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSJ1cmwoI2ZvcmVzdCkiLz4KPC9zdmc+',
    thumbnail:
      'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CiAgPGRlZnM+CiAgICA8bGluZWFyR3JhZGllbnQgaWQ9ImZvcmVzdCIgeDE9IjAlIiB5MT0iMCUiIHgyPSIxMDAlIiB5Mj0iMTAwJSI+CiAgICAgIDxzdG9wIG9mZnNldD0iMCUiIHN0b3AtY29sb3I9IiMxZTM5MmEiLz4KICAgICAgPHN0b3Agb2Zmc2V0PSIxMDAlIiBzdG9wLWNvbG9yPSIjMGYxZjE1Ii8+CiAgICA8L2xpbmVhckdyYWRpZW50PgogIDwvZGVmcz4KICA8cmVjdCB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIGZpbGw9InVybCgjZm9yZXN0KSIvPgo8L3N2Zz4='
  },
  {
    name: '紫色渐变',
    url: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8ZGVmcz4KICAgIDxsaW5lYXJHcmFkaWVudCBpZD0icHVycGxlIiB4MT0iMCUiIHkxPSIwJSIgeDI9IjEwMCUiIHkyPSIxMDAlIj4KICAgICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iIzRjMWQ5NSIvPgogICAgICA8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0b3AtY29sb3I9IiMyMzFhMzciLz4KICAgIDwvbGluZWFyR3JhZGllbnQ+CiAgPC9kZWZzPgogIDxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSJ1cmwoI3B1cnBsZSkiLz4KPC9zdmc+',
    thumbnail:
      'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CiAgPGRlZnM+CiAgICA8bGluZWFyR3JhZGllbnQgaWQ9InB1cnBsZSIgeDE9IjAlIiB5MT0iMCUiIHgyPSIxMDAlIiB5Mj0iMTAwJSI+CiAgICAgIDxzdG9wIG9mZnNldD0iMCUiIHN0b3AtY29sb3I9IiM0YzFkOTUiLz4KICAgICAgPHN0b3Agb2Zmc2V0PSIxMDAlIiBzdG9wLWNvbG9yPSIjMjMxYTM3Ii8+CiAgICA8L2xpbmVhckdyYWRpZW50PgogIDwvZGVmcz4KICA8cmVjdCB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIGZpbGw9InVybCgjcHVycGxlKSIvPgo8L3N2Zz4='
  },
  {
    name: '橙色日落',
    url: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8ZGVmcz4KICAgIDxsaW5lYXJHcmFkaWVudCBpZD0ic3Vuc2V0IiB4MT0iMCUiIHkxPSIwJSIgeDI9IjAlIiB5Mj0iMTAwJSI+CiAgICAgIDxzdG9wIG9mZnNldD0iMCUiIHN0b3AtY29sb3I9IiNmZjZiMzUiLz4KICAgICAgPHN0b3Agb2Zmc2V0PSI1MCUiIHN0b3AtY29sb3I9IiNmOTQ0NDQiLz4KICAgICAgPHN0b3Agb2Zmc2V0PSIxMDAlIiBzdG9wLWNvbG9yPSIjMmQxYjY5Ii8+CiAgICA8L2xpbmVhckdyYWRpZW50PgogIDwvZGVmcz4KICA8cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0idXJsKCNzdW5zZXQpIi8+Cjwvc3ZnPg==',
    thumbnail:
      'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CiAgPGRlZnM+CiAgICA8bGluZWFyR3JhZGllbnQgaWQ9InN1bnNldCIgeDE9IjAlIiB5MT0iMCUiIHgyPSIwJSIgeTI9IjEwMCUiPgogICAgICA8c3RvcCBvZmZzZXQ9IjAlIiBzdG9wLWNvbG9yPSIjZmY2YjM1Ii8+CiAgICAgIDxzdG9wIG9mZnNldD0iNTAlIiBzdG9wLWNvbG9yPSIjZjk0NDQ0Ii8+CiAgICAgIDxzdG9wIG9mZnNldD0iMTAwJSIgc3RvcC1jb2xvcj0iIzJkMWI2OSIvPgogICAgPC9saW5lYXJHcmFkaWVudD4KICA8L2RlZnM+CiAgPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSJ1cmwoI3N1bnNldCkiLz4KPC9zdmc+'
  },
  {
    name: '蓝色波浪',
    url: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8ZGVmcz4KICAgIDxwYXR0ZXJuIGlkPSJ3YXZlcyIgd2lkdGg9IjEwMCIgaGVpZ2h0PSIxMDAiIHBhdHRlcm5Vbml0cz0idXNlclNwYWNlT25Vc2UiPgogICAgICA8cGF0aCBkPSJNMCw1MCBRNTQ1IDI1LDUwIFQ1MCw1MCBRNTQ1IDc1LDUwIFQxMDAsNTAiIHN0cm9rZT0iIzNiODJmNiIgc3Ryb2tlLXdpZHRoPSIzIiBmaWxsPSJub25lIi8+CiAgICAgIDxwYXRoIGQ9Ik0wLDcwIFEyNSw2MCA1MCw3MCBUMTAWLDCWIFEYNSW2MCA3NSw3MCBUMDAWLDCWIHN0cm9rZT0iIzE5NzNmZiIgc3Ryb2tlLXdpZHRoPSIyIiBmaWxsPSJub25lIi8+CiAgICA8L3BhdHRlcm4+CiAgPC9kZWZzPgogIDxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjMGYxNzJhIi8+CiAgPHJlY3Qgd2lkdGg9IjEwMCIgaGVpZ2h0PSIxMDAiIGZpbGw9InVybCgjd2F2ZXMpIi8+Cjwvc3ZnPg==',
    thumbnail:
      'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CiAgPGRlZnM+CiAgICA8cGF0dGVybiBpZD0id2F2ZXMiIHdpZHRoPSI0MCIgaGVpZ2h0PSI0MCIgcGF0dGVyblVuaXRzPSJ1c2VyU3BhY2VPblVzZSI+CiAgICAgIDxwYXRoIGQ9Ik0wLDIwIFE1LDE1IDEwLDIwIFQyMCwyMCBRMjUsMTUgMzAsMjAgVDQwLDIwIiBzdHJva2U9IiMzYjgyZjYiIHN0cm9rZS13aWR0aD0iMiIgZmlsbD0ibm9uZSIvPgogICAgICA8cGF0aCBkPSJNMCwzMCBRNSwyNSAxMCwzMCBUMjAsMzAgUTI1LDI1IDMwLDMwIFQ0MCwzMCIgc3Ryb2tlPSIjMTk3M2ZmIiBzdHJva2Utd2lkdGg9IjEuNSIgZmlsbD0ibm9uZSIvPgogICAgPC9wYXR0ZXJuPgogIDwvZGVmcz4KICA8cmVjdCB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIGZpbGw9IiMwZjE3MmEiLz4KICA8cmVjdCB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIGZpbGw9InVybCgjd2F2ZXMpIi8+Cjwvc3ZnPg=='
  },
  {
    name: '几何图案',
    url: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8ZGVmcz4KICAgIDxwYXR0ZXJuIGlkPSJnZW8iIHdpZHRoPSI1MCIgaGVpZ2h0PSI1MCIgcGF0dGVyblVuaXRzPSJ1c2VyU3BhY2VPblVzZSI+CiAgICAgIDxwb2x5Z29uIHBvaW50cz0iMjUsMCA1MCwyNSAyNSw1MCAMCwyNSIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjNGY0NjU5IiBzdHJva2Utd2lkdGg9IjIiLz4KICAgIDwvcGF0dGVybj4KICA8L2RlZnM+CiAgPHJlY3Qgd2lkdGg9IjEwMCIgaGVpZ2h0PSIxMDAiIGZpbGw9IiMxYTFhMWEiLz4KICA8cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0idXJsKCNnZW8pIi8+Cjwvc3ZnPg==',
    thumbnail:
      'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CiAgPGRlZnM+CiAgICA8cGF0dGVybiBpZD0iZ2VvIiB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHBhdHRlcm5Vbml0cz0idXNlclNwYWNlT25Vc2UiPgogICAgICA8cG9seWdvbiBwb2ludHM9IjEwLDAgMjAsMTAgMTAsMjAgMCwxMCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjNGY0NjU5IiBzdHJva2Utd2lkdGg9IjEiLz4KICAgIDwvcGF0dGVybj4KICA8L2RlZnM+CiAgPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjMWExYTFhIi8+CiAgPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSJ1cmwoI2dlbykiLz4KPC9zdmc+'
  }
]

const selectPresetBackground = (preset) => {
  userConfig.value.terminalBackground.imageUrl = preset.url
}

// 颜色预设
const colorPresets = [
  { label: '蓝色', colors: ['#1890ff', '#40a9ff', '#69c0ff', '#91d5ff'] },
  { label: '绿色', colors: ['#52c41a', '#73d13d', '#95de64', '#b7eb8f'] },
  { label: '红色', colors: ['#f5222d', '#ff4d4f', '#ff7875', '#ffa39e'] },
  { label: '橙色', colors: ['#fa541c', '#ff7a45', '#ff9c6e', '#ffbb96'] },
  { label: '紫色', colors: ['#722ed1', '#9254de', '#b37feb', '#d3adf7'] },
  { label: '灰色', colors: ['#595959', '#8c8c8c', '#bfbfbf', '#d9d9d9'] }
]

// 重置主题颜色到默认值
const resetThemeColors = () => {
  userConfig.value.themeColors = {
    primaryColor: '#1890ff',
    accentColor: '#52c41a',
    backgroundColor: '#141414',
    surfaceColor: '#1f1f1f',
    textColor: '#ffffff',
    borderColor: '#303030'
  }
}

// 应用主题颜色
const applyThemeColors = () => {
  const colors = userConfig.value.themeColors

  // 应用CSS自定义属性 - 使用自定义变量名避免覆盖应用默认主题
  const root = document.documentElement
  root.style.setProperty('--custom-primary-color', colors.primaryColor)
  root.style.setProperty('--custom-accent-color', colors.accentColor)
  root.style.setProperty('--custom-bg-color', colors.backgroundColor)
  root.style.setProperty('--custom-bg-color-secondary', colors.surfaceColor)
  root.style.setProperty('--custom-text-color', colors.textColor)
  root.style.setProperty('--custom-border-color', colors.borderColor)

  // 发送事件通知其他组件
  eventBus.emit('updateThemeColors', colors)

  notification.success({
    message: t('user.success'),
    description: t('user.themeColorsApplied')
  })
}

const columns = [
  {
    title: t('user.fingerprint'),
    dataIndex: 'fingerprint',
    key: 'fingerprint'
  },
  {
    title: t('user.comment'),
    dataIndex: 'comment',
    key: 'comment'
  },
  {
    title: t('user.type'),
    dataIndex: 'keyType',
    key: 'keyType'
  },
  {
    title: t('extensions.action'),
    dataIndex: 'action',
    key: 'action'
  }
]

const proxyConfigColumns = [
  {
    title: t('user.proxyName'),
    dataIndex: 'name',
    key: 'name'
  },
  {
    title: t('user.proxyType'),
    dataIndex: 'type',
    key: 'type'
  },
  {
    title: t('user.proxyHost'),
    dataIndex: 'host',
    key: 'host'
  },
  {
    title: t('user.proxyPort'),
    dataIndex: 'port',
    key: 'port'
  },
  {
    title: t('user.proxyUsername'),
    dataIndex: 'username',
    key: 'username'
  },
  {
    title: t('extensions.action'),
    dataIndex: 'action',
    key: 'action'
  }
]

// Load saved configuration
const loadSavedConfig = async () => {
  try {
    const savedConfig = await userConfigStore.getConfig()
    if (savedConfig) {
      userConfig.value = {
        ...userConfig.value,
        ...savedConfig,
        // 确保terminalBackground字段正确初始化
        terminalBackground: savedConfig.terminalBackground || {
          type: 'default',
          color: '#1a1a1a',
          imageUrl: '',
          opacity: 1.0
        },
        // 确保themeColors字段正确初始化
        themeColors: savedConfig.themeColors || {
          primaryColor: '#1890ff',
          accentColor: '#52c41a',
          backgroundColor: '#141414',
          surfaceColor: '#1f1f1f',
          textColor: '#ffffff',
          borderColor: '#303030'
        }
      }

      // 注释掉自动应用主题颜色，避免覆盖应用默认主题
      // if (savedConfig.themeColors) {
      //   applyThemeColors()
      // }
    }
  } catch (error) {
    console.error('Failed to load config:', error)
    notification.error({
      message: t('user.loadConfigFailed'),
      description: t('user.loadConfigFailedDescription')
    })
  }
}

const handleSshAgentsStatusChange = async (checked) => {
  userConfig.value.sshAgentsStatus = checked ? 1 : 2
  window.api.agentEnableAndConfigure({ enabled: checked }).then((res) => {
    if (checked && res.success) {
      const sshAgentMaps = JSON.parse(userConfig.value.sshAgentsMap)
      for (const keyId in sshAgentMaps) {
        loadKey(sshAgentMaps[keyId])
      }
    }
  })
}

// ssh代理
const sshProxyConfigAddModalVisible = ref(false)
const sshProxyConfigShowModalVisible = ref(false)
const openProxyConfig = async () => {
  sshProxyConfigShowModalVisible.value = true
}

const handleProxyConfigClose = async () => {
  sshProxyConfigShowModalVisible.value = false
}
//
const proxyConfigRules = {
  name: [
    { required: true, message: t('user.pleaseInputProxyName'), trigger: 'blur' },
    {
      validator: (rule, value) => {
        if (!value) return Promise.resolve()

        const nameExists = userConfig.value.sshProxyConfigs.some((config) => config.name === value)

        if (nameExists) {
          return Promise.reject(new Error(t('user.pleaseInputOtherProxyName')))
        }
        return Promise.resolve()
      },
      trigger: 'blur'
    }
  ],
  host: [{ required: true, message: t('user.pleaseInputProxyHost'), trigger: 'blur' }],
  port: [{ type: 'number', min: 1, max: 65535, message: t('user.errorProxyPort'), trigger: 'blur' }]
}

const handleProxyConfigAdd = async () => {
  sshProxyConfigAddModalVisible.value = true
}

const handleSshProxyIdentityChange = async (checked) => {
  proxyConfig.value.enableProxyIdentity = checked
}

const proxyForm = ref()
const handleAddSshProxyConfigConfirm = async () => {
  await proxyForm.value.validateFields()
  userConfig.value.sshProxyConfigs.push(proxyConfig.value)
  sshProxyConfigAddModalVisible.value = false
  proxyConfig.value = defaultProxyConfig
}

const removeProxyConfig = (proxyName) => {
  const index = userConfig.value.sshProxyConfigs.findIndex((config) => config.name === proxyName)

  if (index !== -1) {
    userConfig.value.sshProxyConfigs.splice(index, 1)
    return true
  } else {
    return false
  }
}

const handleAddSshProxyConfigClose = async () => {
  sshProxyConfigAddModalVisible.value = false
}

const keyChainOptions = ref([])
const agentKeys = ref([])
const keyChainData = ref()

const openAgentConfig = async () => {
  agentConfigModalVisible.value = true
  getKeyChainData()
  await getAgentKeys()
}

const handleAgentConfigClose = async () => {
  agentConfigModalVisible.value = false
}

const removeKey = async (record) => {
  await api.removeKey({ keyId: record.id })
  const target = keyChainOptions.value.find((item) => item.label === record.comment)

  if (target) {
    const sshAgentsMap = JSON.parse(userConfig.value.sshAgentsMap)
    const index = sshAgentsMap.indexOf(target.key)
    if (index !== -1) {
      sshAgentsMap.splice(index, 1)
    }
    userConfig.value.sshAgentsMap = JSON.stringify(sshAgentsMap)
  }
  await getAgentKeys()
}

const loadKey = async (keyId) => {
  await window.api.getKeyChainInfo({ id: keyId }).then((res) => {
    window.api.addKey({
      keyData: res.private_key,
      comment: res.chain_name,
      passphrase: res.passphrase
    })
  })
}
const addKey = async () => {
  if (keyChainData.value) {
    await api.getKeyChainInfo({ id: keyChainData.value }).then((res) => {
      api
        .addKey({
          keyData: res.private_key,
          comment: res.chain_name,
          passphrase: res.passphrase
        })
        .then(() => {
          notification.success({
            message: t('user.addSuccess')
          })
          let sshAgentKey = JSON.parse(userConfig.value.sshAgentsMap)
          sshAgentKey.push(keyChainData.value)
          sshAgentKey = Array.from(new Set(sshAgentKey))
          userConfig.value.sshAgentsMap = JSON.stringify(sshAgentKey)
          keyChainData.value = null
          getAgentKeys()
        })
        .catch(() => {
          notification.error({
            message: t('user.addFailed')
          })
          keyChainData.value = null
        })
    })
  }
}
const getAgentKeys = async () => {
  const res = await api.listKeys()
  agentKeys.value = res.keys
}

const getKeyChainData = () => {
  api.getKeyChainSelect().then((res) => {
    keyChainOptions.value = res.data.keyChain
  })
}

const handleImageUpload = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      userConfig.value.terminalBackground.imageUrl = e.target.result
      resolve(false) // 阻止默认上传行为
    }
    reader.onerror = () => {
      notification.error({
        message: t('user.error'),
        description: '图片上传失败'
      })
      reject(false)
    }
    reader.readAsDataURL(file)
  })
}

const saveConfig = async () => {
  try {
    const configToStore = {
      fontSize: userConfig.value.fontSize,
      fontFamily: userConfig.value.fontFamily,
      scrollBack: userConfig.value.scrollBack,
      cursorStyle: userConfig.value.cursorStyle,
      middleMouseEvent: userConfig.value.middleMouseEvent,
      rightMouseEvent: userConfig.value.rightMouseEvent,
      terminalType: userConfig.value.terminalType,
      sshAgentsStatus: userConfig.value.sshAgentsStatus,
      sshAgentsMap: userConfig.value.sshAgentsMap,
      sshProxyConfigs: userConfig.value.sshProxyConfigs,
      terminalBackground: userConfig.value.terminalBackground,
      themeColors: userConfig.value.themeColors
    }

    await userConfigStore.saveConfig(configToStore)

    // 发送事件通知终端组件更新背景
    eventBus.emit('updateTerminalBackground', userConfig.value.terminalBackground)
  } catch (error) {
    console.error('Failed to save config:', error)
    notification.error({
      message: t('user.error'),
      description: t('user.saveConfigFailedDescription')
    })
  }
}

watch(
  () => userConfig.value,
  async () => {
    await saveConfig()
  },
  { deep: true }
)

watch(
  () => userConfig.value.fontFamily,
  (newFontFamily) => {
    eventBus.emit('updateTerminalFont', newFontFamily)
  }
)

onMounted(async () => {
  console.log('Terminal component mounted')
  try {
    await loadSavedConfig()
    console.log('Terminal config loaded:', userConfig.value)
  } catch (error) {
    console.error('Terminal component mount error:', error)
  }
})
</script>

<style scoped>
.userInfo {
  width: 100%;
  height: 100%;
}

.userInfo-container {
  width: 100%;
  height: 100%;
  background-color: var(--bg-color) !important;
  border-radius: 6px;
  overflow: hidden;
  padding: 4px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
  color: var(--text-color);
}

:deep(.ant-card) {
  height: 100%;
  background-color: var(--bg-color) !important;
}

:deep(.ant-card-body) {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--bg-color);
}

.proxy-form :deep(.ant-form-item-label > label) {
  color: #000000 !important;
}

.custom-form {
  color: var(--text-color);
  align-content: center;
}

.custom-form :deep(.ant-form-item-label) {
  padding-right: 20px;
}

.custom-form :deep(.ant-form-item-label > label) {
  color: var(--text-color);
}

.custom-form :deep(.ant-input),
.custom-form :deep(.ant-input-number),
.custom-form :deep(.ant-radio-wrapper) {
  color: var(--text-color);
}

.custom-form :deep(.ant-input-number) {
  background-color: var(--input-number-bg);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  transition: all 0.3s;
  width: 100px !important;
}

.custom-form :deep(.ant-input-number:hover),
.custom-form :deep(.ant-input-number:focus),
.custom-form :deep(.ant-input-number-focused) {
  background-color: var(--input-number-hover-bg);
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.custom-form :deep(.ant-input-number-input) {
  height: 32px;
  padding: 4px 8px;
  background-color: transparent;
  color: var(--text-color);
}

.label-text {
  font-size: 20px;
  font-weight: bold;
  line-height: 1.3;
}

.user_my-ant-form-item {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  color: rgba(0, 0, 0, 0.65);
  font-size: 30px;
  font-variant: tabular-nums;
  line-height: 1.5;
  list-style: none;
  -webkit-font-feature-settings: 'tnum';
  font-feature-settings: 'tnum';
  margin-bottom: 14px;
  vertical-align: top;
  color: #ffffff;
}

.terminal-type-select {
  width: 180px !important;
  text-align: left;
}

.font-family-select {
  width: 180px !important;
  text-align: left;
}

.font-family-select :deep(.ant-select-selector) {
  background-color: var(--select-bg);
  border: 1px solid var(--select-border);
  border-radius: 6px;
  color: var(--text-color);
  transition: all 0.3s;
  height: 32px;
}

.font-family-select :deep(.ant-select-selector:hover) {
  border-color: #1890ff;
  background-color: var(--select-hover-bg);
}

.font-family-select :deep(.ant-select-focused .ant-select-selector),
.font-family-select :deep(.ant-select-selector:focus) {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  background-color: var(--select-hover-bg);
}

.font-family-select :deep(.ant-select-selection-item) {
  color: var(--text-color);
  font-size: 14px;
  line-height: 32px;
}

.font-family-select :deep(.ant-select-arrow) {
  color: var(--text-color);
  opacity: 0.7;
}

.divider-container {
  width: calc(65%);
  margin: -10px calc(16%);
}

:deep(.right-aligned-wrapper) {
  text-align: right;
  color: #ffffff;
}

.checkbox-md :deep(.ant-checkbox-inner) {
  width: 20px;
  height: 20px;
}

.telemetry-description-item {
  margin-top: -15px;
  margin-bottom: 14px;
}

.telemetry-description-item :deep(.ant-form-item-control) {
  margin-left: 0 !important;
  max-width: 100% !important;
}

.telemetry-description {
  font-size: 12px;
  color: var(--text-color-secondary);
  line-height: 1.4;
  opacity: 0.8;
  text-align: left;
  margin: 0;
  margin-left: 20px;
  padding: 0;
  word-wrap: break-word;
}

.telemetry-description a {
  color: #1890ff;
  text-decoration: none;
  transition: color 0.3s;
}

.telemetry-description a:hover {
  color: #40a9ff;
  text-decoration: underline;
}

.mouse-event-row {
  margin-bottom: 10px;
  min-height: 32px;
}

.mouse-event-label {
  font-size: 14px;
  color: var(--text-color);
  min-width: 110px;
  text-align: left;
  opacity: 0.9;
  margin-right: 10px;
}

.mouse-event-select {
  width: 140px;
}

.mouse-event-select :deep(.ant-select-selector) {
  background-color: var(--select-bg);
  border: 1px solid var(--select-border);
  border-radius: 6px;
  color: var(--text-color);
  transition: all 0.3s;
  height: 32px;
}

.mouse-event-select :deep(.ant-select-selector:hover),
.mouse-event-select :deep(.ant-select-focused .ant-select-selector) {
  background-color: var(--select-hover-bg);
  border-color: #1890ff;
}

.mouse-event-select :deep(.ant-select-selection-item) {
  color: var(--text-color);
  font-size: 14px;
  line-height: 32px;
}

.mouse-event-select :deep(.ant-select-arrow) {
  color: var(--text-color);
  opacity: 0.7;
}

:deep(.ant-select .ant-select-selector) {
  background-color: var(--bg-color-secondary) !important;
  border: 1px solid var(--border-color);
  color: var(--text-color);
}

:deep(.ant-select.ant-select-focused .ant-select-selector) {
  background-color: var(--bg-color-secondary) !important;
  border-color: #1890ff !important;
}
::v-deep(.agent-table .ant-table-tbody > tr > td),
::v-deep(.agent-table .ant-table-thead > tr > th) {
  padding-top: 2px !important;
  padding-bottom: 2px !important;
  line-height: 1 !important;
  font-size: 12px !important;
}
.agent-table .ant-table-tbody > tr {
  height: 28px !important;
}

.proxy-form :deep(:where(.ant-form-item)) {
  margin-bottom: 12px !important;
}

/* Setting button styles - consistent with host management import button */
.setting-button {
  display: flex;
  align-items: center;
  gap: 4px;
  height: 32px;
  padding: 0 12px;
  border-radius: 4px;
  background: var(--bg-color);
  border: 1px solid var(--border-color);
  color: var(--text-color);
  transition: all 0.3s ease;
  /* Ensure button inherits right alignment from parent */
  margin-left: auto;
  margin-right: 0;
}

.setting-button:hover {
  background: var(--hover-bg-color);
  border-color: #1890ff;
  color: #1890ff;
}

.setting-button:active {
  background: var(--active-bg-color);
}

/* Override Ant Design default button styles for setting buttons */
.setting-button:deep(.ant-btn) {
  background: var(--bg-color) !important;
  border-color: var(--border-color) !important;
  color: var(--text-color) !important;
}

/* Ensure setting button form items inherit right alignment */
.user_my-ant-form-item:has(.setting-button) {
  text-align: right;
}

.user_my-ant-form-item:has(.setting-button) :deep(.ant-form-item-control) {
  text-align: right;
}

.setting-button:deep(.ant-btn:hover) {
  background: var(--hover-bg-color) !important;
  border-color: #1890ff !important;
  color: #1890ff !important;
}

.setting-button:deep(.ant-btn:active) {
  background: var(--active-bg-color) !important;
}

/* Terminal background settings styles */
.terminal-background-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.background-type-row,
.background-color-row,
.background-image-row,
.background-opacity-row {
  display: flex;
  align-items: center;
  gap: 10px;
  min-height: 32px;
}

.background-label {
  font-size: 14px;
  color: var(--text-color);
  min-width: 120px;
  text-align: left;
  opacity: 0.9;
}

.background-type-select {
  width: 140px;
}

.background-type-select :deep(.ant-select-selector) {
  background-color: var(--select-bg);
  border: 1px solid var(--select-border);
  border-radius: 6px;
  color: var(--text-color);
  transition: all 0.3s;
  height: 32px;
}

.background-type-select :deep(.ant-select-selector:hover),
.background-type-select :deep(.ant-select-focused .ant-select-selector) {
  background-color: var(--select-hover-bg);
  border-color: #1890ff;
}

.background-type-select :deep(.ant-select-selection-item) {
  color: var(--text-color);
  font-size: 14px;
  line-height: 32px;
}

.background-type-select :deep(.ant-select-arrow) {
  color: var(--text-color);
  opacity: 0.7;
}

.color-picker {
  width: 60px;
  height: 32px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: transparent;
  cursor: pointer;
  transition: all 0.3s;
}

.color-picker:hover {
  border-color: #1890ff;
}

.image-input-container {
  display: flex;
  gap: 8px;
  align-items: center;
  flex: 1;
}

.image-url-input {
  flex: 1;
  max-width: 200px;
}

.image-url-input :deep(.ant-input) {
  background-color: var(--input-bg);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  color: var(--text-color);
  transition: all 0.3s;
}

.image-url-input :deep(.ant-input:hover),
.image-url-input :deep(.ant-input:focus) {
  border-color: #1890ff;
  background-color: var(--input-hover-bg);
}

.upload-button {
  height: 32px;
  padding: 0 12px;
  border-radius: 4px;
  background: var(--bg-color);
  border: 1px solid var(--border-color);
  color: var(--text-color);
  transition: all 0.3s ease;
  font-size: 12px;
}

.upload-button:hover {
  background: var(--hover-bg-color);
  border-color: #1890ff;
  color: #1890ff;
}

.opacity-slider {
  width: 120px;
}

.opacity-slider :deep(.ant-slider-rail) {
  background-color: var(--border-color);
}

.opacity-slider :deep(.ant-slider-track) {
  background-color: #1890ff;
}

.opacity-slider :deep(.ant-slider-handle) {
  border-color: #1890ff;
  background-color: #1890ff;
}

.opacity-value {
  font-size: 12px;
  color: var(--text-color);
  min-width: 35px;
  text-align: center;
}

/* Preset backgrounds styles */
.background-presets-container {
  width: 100%;
  margin-top: 12px;
}

.background-presets-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.preset-backgrounds {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  width: 100%;
  padding: 0;
  margin: 0;
  justify-content: flex-start;
}

.preset-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  padding: 10px;
  border: 2px solid var(--border-color);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: var(--bg-color-secondary);
  min-width: 80px;
  position: relative;
  z-index: 1;
}

.preset-item:hover {
  border-color: #1890ff;
  background: var(--hover-bg-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.2);
}

.preset-item.active {
  border-color: #1890ff;
  background: var(--active-bg-color);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.3);
}

.preset-thumbnail {
  width: 50px;
  height: 50px;
  border-radius: 6px;
  object-fit: cover;
  border: 1px solid var(--border-color);
}

.preset-name {
  font-size: 11px;
  color: var(--text-color);
  text-align: center;
  font-weight: 500;
  white-space: nowrap;
}

/* Theme colors styles */
.theme-colors-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px;
  background: var(--bg-color-secondary);
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.theme-color-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
}

.color-label {
  font-size: 14px;
  color: var(--text-color);
  font-weight: 500;
  min-width: 100px;
}

.theme-color-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  margin-top: 8px;
  padding-top: 12px;
  border-top: 1px solid var(--border-color);
}

.theme-color-actions .ant-btn {
  min-width: 80px;
}
</style>
