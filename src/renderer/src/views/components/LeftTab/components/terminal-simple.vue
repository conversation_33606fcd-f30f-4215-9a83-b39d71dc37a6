<template>
  <div class="userInfo">
    <a-card
      :bordered="false"
      class="userInfo-container"
    >
      <a-form
        :colon="false"
        label-align="left"
        wrapper-align="right"
        :label-col="{ span: 7, offset: 0 }"
        :wrapper-col="{ span: 17, class: 'right-aligned-wrapper' }"
        class="custom-form"
      >
        <a-form-item>
          <template #label>
            <span class="label-text">终端设置</span>
          </template>
        </a-form-item>

        <a-form-item
          label="终端类型"
          class="user_my-ant-form-item"
        >
          <a-select
            v-model:value="userConfig.terminalType"
            class="terminal-type-select"
          >
            <a-select-option value="xterm">xterm</a-select-option>
            <a-select-option value="xterm-256color">xterm-256color</a-select-option>
            <a-select-option value="vt100">vt100</a-select-option>
            <a-select-option value="vt102">vt102</a-select-option>
            <a-select-option value="vt220">vt220</a-select-option>
            <a-select-option value="vt320">vt320</a-select-option>
            <a-select-option value="linux">linux</a-select-option>
            <a-select-option value="scoansi">scoansi</a-select-option>
            <a-select-option value="ansi">ansi</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item
          label="字体"
          class="user_my-ant-form-item"
        >
          <a-select
            v-model:value="userConfig.fontFamily"
            class="font-family-select"
            :options="fontFamilyOptions"
          />
        </a-form-item>

        <a-form-item
          label="字体大小(px)"
          class="user_my-ant-form-item"
        >
          <a-input-number
            v-model:value="userConfig.fontSize"
            :bordered="false"
            style="width: 20%"
            :min="8"
            :max="64"
            class="user_my-ant-form-item-content"
          />
        </a-form-item>

        <a-form-item
          label="终端回滚"
          class="user_my-ant-form-item"
        >
          <a-input-number
            v-model:value="userConfig.scrollBack"
            :bordered="false"
            style="width: 20%"
            :min="1"
            class="user_my-ant-form-item-content"
          />
        </a-form-item>

        <a-form-item
          label="光标样式"
          class="user_my-ant-form-item"
        >
          <a-radio-group
            v-model:value="userConfig.cursorStyle"
            class="custom-radio-group"
          >
            <a-radio value="bar">条</a-radio>
            <a-radio value="block">实线</a-radio>
            <a-radio value="underline">下划线</a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item
          label="SSH Agents"
          class="user_my-ant-form-item"
        >
          <a-switch
            :checked="userConfig.sshAgentsStatus === 1"
            class="user_my-ant-form-item-content"
            @change="handleSshAgentsStatusChange"
          />
        </a-form-item>

        <a-form-item
          label="鼠标事件"
          class="user_my-ant-form-item"
        >
          <div class="mouse-events-container">
            <div class="mouse-event-row">
              <span class="mouse-event-label">鼠标右键事件:</span>
              <a-select
                v-model:value="userConfig.middleMouseEvent"
                class="mouse-event-select"
              >
                <a-select-option value="paste">粘贴内容</a-select-option>
                <a-select-option value="contextMenu">右键菜单</a-select-option>
                <a-select-option value="none">无</a-select-option>
              </a-select>
            </div>
            <div class="mouse-event-row">
              <span class="mouse-event-label">鼠标右键事件:</span>
              <a-select
                v-model:value="userConfig.rightMouseEvent"
                class="mouse-event-select"
              >
                <a-select-option value="contextMenu">右键菜单</a-select-option>
                <a-select-option value="paste">粘贴内容</a-select-option>
                <a-select-option value="none">无</a-select-option>
              </a-select>
            </div>
          </div>
        </a-form-item>

        <!-- 终端背景设置 -->
        <a-form-item
          label="终端背景"
          class="user_my-ant-form-item"
        >
          <div class="terminal-background-container">
            <div class="background-type-row">
              <span class="background-label">背景类型:</span>
              <a-select
                v-model:value="userConfig.terminalBackground.type"
                class="background-type-select"
              >
                <a-select-option value="default">默认</a-select-option>
                <a-select-option value="color">纯色背景</a-select-option>
                <a-select-option value="image">图片背景</a-select-option>
              </a-select>
            </div>
            <div
              v-if="userConfig.terminalBackground.type === 'color'"
              class="background-color-row"
            >
              <span class="background-label">背景颜色:</span>
              <input
                v-model="userConfig.terminalBackground.color"
                type="color"
                class="color-picker"
              />
            </div>
            <div
              v-if="userConfig.terminalBackground.type === 'image'"
              class="background-image-row"
            >
              <span class="background-label">背景图片URL:</span>
              <a-input
                v-model:value="userConfig.terminalBackground.imageUrl"
                placeholder="请输入图片URL或使用下方上传"
                class="image-url-input"
              />
              <a-upload
                :show-upload-list="false"
                :before-upload="handleImageUpload"
                accept="image/*"
              >
                <a-button
                  size="small"
                  class="upload-button"
                >
                  上传图片
                </a-button>
              </a-upload>
            </div>
            <div class="background-opacity-row">
              <span class="background-label">背景透明度:</span>
              <a-slider
                v-model:value="userConfig.terminalBackground.opacity"
                :min="0.1"
                :max="1"
                :step="0.1"
                class="opacity-slider"
              />
            </div>
          </div>
        </a-form-item>

        <!-- 背景预设 -->
        <a-form-item
          :label="'背景预设'"
          class="user_my-ant-form-item"
        >
          <div class="background-presets-container">
            <div class="preset-grid">
              <div
                v-for="preset in backgroundPresets"
                :key="preset.name"
                class="preset-item"
                :class="{ active: isPresetActive(preset) }"
                @click="applyBackgroundPreset(preset)"
              >
                <div
                  class="preset-preview"
                  :style="getPresetStyle(preset)"
                ></div>
                <span class="preset-name">{{ preset.name }}</span>
              </div>
            </div>
          </div>
        </a-form-item>
      </a-form>
    </a-card>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { userConfigStore } from '@/services/userConfigStoreService'
import eventBus from '@/utils/eventBus'

const { t } = useI18n()

// 字体选项
const fontFamilyOptions = ref([
  { value: 'Menlo, Monaco, "Courier New", Consolas, Courier, monospace', label: 'Menlo (默认)' },
  { value: 'Monaco, "Courier New", Consolas, Courier, monospace', label: 'Monaco' },
  { value: 'Consolas, "Courier New", Courier, monospace', label: 'Consolas' },
  { value: '"Courier New", Courier, monospace', label: 'Courier New' },
  { value: '"SF Mono", Monaco, Menlo, Consolas, "Ubuntu Mono", monospace', label: 'SF Mono' },
  { value: '"JetBrains Mono", Monaco, Menlo, Consolas, monospace', label: 'JetBrains Mono' }
])

// 用户配置
const userConfig = ref({
  fontSize: 12,
  fontFamily: 'Menlo, Monaco, "Courier New", Consolas, Courier, monospace',
  scrollBack: 1000,
  cursorStyle: 'block',
  middleMouseEvent: 'paste',
  rightMouseEvent: 'contextMenu',
  terminalType: 'vt100',
  sshAgentsStatus: 2,
  terminalBackground: {
    type: 'default',
    color: '#1a1a1a',
    imageUrl: '',
    opacity: 1.0
  },
  themeColors: {
    primaryColor: '#1890ff',
    accentColor: '#52c41a',
    backgroundColor: '#141414',
    surfaceColor: '#1f1f1f',
    textColor: '#ffffff',
    borderColor: '#303030'
  }
})

// 现代化背景预设
const backgroundPresets = ref([
  {
    name: '默认',
    type: 'default',
    color: '#1a1a1a',
    imageUrl: '',
    opacity: 1.0
  },
  {
    name: '深空蓝',
    type: 'color',
    color: '#0a0e27',
    imageUrl: '',
    opacity: 1.0
  },
  {
    name: '赛博朋克',
    type: 'color',
    color: '#0d1421',
    imageUrl: '',
    opacity: 1.0
  },
  {
    name: '暗夜绿',
    type: 'color',
    color: '#0f1b0c',
    imageUrl: '',
    opacity: 1.0
  },
  {
    name: '紫罗兰',
    type: 'color',
    color: '#1a0d2e',
    imageUrl: '',
    opacity: 1.0
  },
  {
    name: '深海蓝',
    type: 'color',
    color: '#0c1618',
    imageUrl: '',
    opacity: 1.0
  },
  {
    name: '炭黑',
    type: 'color',
    color: '#1c1c1c',
    imageUrl: '',
    opacity: 1.0
  },
  {
    name: '霓虹网格',
    type: 'image',
    color: '#000814',
    imageUrl:
      'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjMDAwODE0Ii8+CjxkZWZzPgo8bGluZWFyR3JhZGllbnQgaWQ9ImdyaWQiIHgxPSIwJSIgeTE9IjAlIiB4Mj0iMTAwJSIgeTI9IjEwMCUiPgo8c3RvcCBvZmZzZXQ9IjAlIiBzdHlsZT0ic3RvcC1jb2xvcjojMDBmZmZmO3N0b3Atb3BhY2l0eTowLjMiLz4KPHN0b3Agb2Zmc2V0PSIxMDAlIiBzdHlsZT0ic3RvcC1jb2xvcjojZmYwMGZmO3N0b3Atb3BhY2l0eTowLjEiLz4KPC9saW5lYXJHcmFkaWVudD4KPC9kZWZzPgo8cGF0aCBkPSJNIDAgMCBMIDAgNDAgTSAwIDAgTCA0MCAwIE0gMjAgMCBMIDIwIDQwIE0gMCAyMCBMIDQwIDIwIiBzdHJva2U9InVybCgjZ3JpZCkiIHN0cm9rZS13aWR0aD0iMC41Ii8+Cjwvc3ZnPgo=',
    opacity: 0.9
  },
  {
    name: '数字雨',
    type: 'image',
    color: '#000000',
    imageUrl:
      'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCAzMCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjMwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjMDAwMDAwIi8+Cjx0ZXh0IHg9IjUiIHk9IjEwIiBmb250LWZhbWlseT0ibW9ub3NwYWNlIiBmb250LXNpemU9IjgiIGZpbGw9IiMwMGZmNDEiIG9wYWNpdHk9IjAuNyI+MTwvdGV4dD4KPHR4dCB4PSIxNSIgeT0iMjAiIGZvbnQtZmFtaWx5PSJtb25vc3BhY2UiIGZvbnQtc2l6ZT0iNiIgZmlsbD0iIzAwZmY0MSIgb3BhY2l0eT0iMC41Ij4wPC90ZXh0Pgo8dGV4dCB4PSIyNSIgeT0iMzAiIGZvbnQtZmFtaWx5PSJtb25vc3BhY2UiIGZvbnQtc2l6ZT0iNyIgZmlsbD0iIzAwZmY0MSIgb3BhY2l0eT0iMC42Ij4xPC90ZXh0Pgo8dGV4dCB4PSI4IiB5PSIzNSIgZm9udC1mYW1pbHk9Im1vbm9zcGFjZSIgZm9udC1zaXplPSI1IiBmaWxsPSIjMDBmZjQxIiBvcGFjaXR5PSIwLjQiPjA8L3RleHQ+Cjwvc3ZnPgo=',
    opacity: 0.8
  },
  {
    name: '星云',
    type: 'image',
    color: '#0a0a2e',
    imageUrl:
      'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjYwIiBoZWlnaHQ9IjYwIiBmaWxsPSIjMGEwYTJlIi8+CjxkZWZzPgo8cmFkaWFsR3JhZGllbnQgaWQ9Im5lYnVsYSIgY3g9IjUwJSIgY3k9IjUwJSIgcj0iNTAlIj4KPHN0b3Agb2Zmc2V0PSIwJSIgc3R5bGU9InN0b3AtY29sb3I6IzY2MDBmZjtzdG9wLW9wYWNpdHk6MC4zIi8+CjxzdG9wIG9mZnNldD0iNTAlIiBzdHlsZT0ic3RvcC1jb2xvcjojMDA4OGZmO3N0b3Atb3BhY2l0eTowLjIiLz4KPHN0b3Agb2Zmc2V0PSIxMDAlIiBzdHlsZT0ic3RvcC1jb2xvcjojZmYwMGZmO3N0b3Atb3BhY2l0eTowLjEiLz4KPC9yYWRpYWxHcmFkaWVudD4KPC9kZWZzPgo8Y2lyY2xlIGN4PSIzMCIgY3k9IjMwIiByPSIyNSIgZmlsbD0idXJsKCNuZWJ1bGEpIi8+CjxjaXJjbGUgY3g9IjEwIiBjeT0iMTAiIHI9IjEiIGZpbGw9IiNmZmZmZmYiIG9wYWNpdHk9IjAuOSIvPgo8Y2lyY2xlIGN4PSI1MCIgY3k9IjE1IiByPSIwLjUiIGZpbGw9IiNmZmZmZmYiIG9wYWNpdHk9IjAuNyIvPgo8Y2lyY2xlIGN4PSI0NSIgY3k9IjQ1IiByPSIwLjgiIGZpbGw9IiNmZmZmZmYiIG9wYWNpdHk9IjAuOCIvPgo8L3N2Zz4K',
    opacity: 0.9
  },
  {
    name: '电路板',
    type: 'image',
    color: '#0d1b2a',
    imageUrl:
      'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjMGQxYjJhIi8+CjxwYXRoIGQ9Ik0gMTAgMTAgTCAzMCAxMCBNIDEwIDEwIEwgMTAgMzAgTSAzMCAxMCBMIDMwIDMwIE0gMTAgMzAgTCAzMCAzMCIgc3Ryb2tlPSIjMDBmZjg4IiBzdHJva2Utd2lkdGg9IjEiIG9wYWNpdHk9IjAuNCIvPgo8Y2lyY2xlIGN4PSIxMCIgY3k9IjEwIiByPSIyIiBmaWxsPSIjMDBmZjg4IiBvcGFjaXR5PSIwLjYiLz4KPGNpcmNsZSBjeD0iMzAiIGN5PSIxMCIgcj0iMiIgZmlsbD0iIzAwZmY4OCIgb3BhY2l0eT0iMC42Ii8+CjxjaXJjbGUgY3g9IjEwIiBjeT0iMzAiIHI9IjIiIGZpbGw9IiMwMGZmODgiIG9wYWNpdHk9IjAuNiIvPgo8Y2lyY2xlIGN4PSIzMCIgY3k9IjMwIiByPSIyIiBmaWxsPSIjMDBmZjg4IiBvcGFjaXR5PSIwLjYiLz4KPC9zdmc+Cg==',
    opacity: 0.8
  },
  {
    name: '波浪',
    type: 'image',
    color: '#0f172a',
    imageUrl:
      'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCA2MCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjYwIiBoZWlnaHQ9IjIwIiBmaWxsPSIjMGYxNzJhIi8+CjxwYXRoIGQ9Ik0gMCAxMCBRIDE1IDUgMzAgMTAgVCA2MCAxMCIgc3Ryb2tlPSIjMzMzOGZmIiBzdHJva2Utd2lkdGg9IjEiIGZpbGw9Im5vbmUiIG9wYWNpdHk9IjAuNCIvPgo8cGF0aCBkPSJNIDAgMTUgUSAxNSAxMCAzMCAxNSBUIDYwIDE1IiBzdHJva2U9IiMzMzM4ZmYiIHN0cm9rZS13aWR0aD0iMSIgZmlsbD0ibm9uZSIgb3BhY2l0eT0iMC4yIi8+Cjwvc3ZnPgo=',
    opacity: 0.9
  },
  {
    name: '六边形',
    type: 'image',
    color: '#1a1a2e',
    imageUrl:
      'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjMWExYTJlIi8+Cjxwb2x5Z29uIHBvaW50cz0iMjAsMTAgMzAsMTUgMzAsMjUgMjAsMzAgMTAsMjUgMTAsMTUiIHN0cm9rZT0iIzAwZmZkNCIgc3Ryb2tlLXdpZHRoPSIxIiBmaWxsPSJub25lIiBvcGFjaXR5PSIwLjMiLz4KPC9zdmc+Cg==',
    opacity: 0.8
  },
  {
    name: '粒子',
    type: 'image',
    color: '#16213e',
    imageUrl:
      'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIHZpZXdCb3g9IjAgMCA1MCA1MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjUwIiBoZWlnaHQ9IjUwIiBmaWxsPSIjMTYyMTNlIi8+CjxjaXJjbGUgY3g9IjEwIiBjeT0iMTAiIHI9IjEuNSIgZmlsbD0iIzAwZmZmZiIgb3BhY2l0eT0iMC43Ij4KPGFUAW1hdGUgYXR0cmlidXRlTmFtZT0ib3BhY2l0eSIgdmFsdWVzPSIwLjM7MC44OzAuMyIgZHVyPSIycyIgcmVwZWF0Q291bnQ9ImluZGVmaW5pdGUiLz4KPC9jaXJjbGU+CjxjaXJjbGUgY3g9IjQwIiBjeT0iMTUiIHI9IjEiIGZpbGw9IiNmZjAwZmYiIG9wYWNpdHk9IjAuNiI+CjxhbmltYXRlIGF0dHJpYnV0ZU5hbWU9Im9wYWNpdHkiIHZhbHVlcz0iMC4yOzAuNzswLjIiIGR1cj0iM3MiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIi8+CjwvY2lyY2xlPgo8Y2lyY2xlIGN4PSIyNSIgY3k9IjM1IiByPSIxLjIiIGZpbGw9IiNmZmZmMDAiIG9wYWNpdHk9IjAuNSI+CjxhbmltYXRlIGF0dHJpYnV0ZU5hbWU9Im9wYWNpdHkiIHZhbHVlcz0iMC4xOzAuNjswLjEiIGR1cj0iMi41cyIgcmVwZWF0Q291bnQ9ImluZGVmaW5pdGUiLz4KPC9jaXJjbGU+Cjwvc3ZnPgo=',
    opacity: 0.9
  },
  {
    name: '渐变网格',
    type: 'image',
    color: '#0f0f23',
    imageUrl:
      'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAiIGhlaWdodD0iMzAiIHZpZXdCb3g9IjAgMCAzMCAzMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjMwIiBoZWlnaHQ9IjMwIiBmaWxsPSIjMGYwZjIzIi8+CjxkZWZzPgo8bGluZWFyR3JhZGllbnQgaWQ9ImdyYWRHcmlkIiB4MT0iMCUiIHkxPSIwJSIgeDI9IjEwMCUiIHkyPSIxMDAlIj4KPHN0b3Agb2Zmc2V0PSIwJSIgc3R5bGU9InN0b3AtY29sb3I6IzY2NjZmZjtzdG9wLW9wYWNpdHk6MC4zIi8+CjxzdG9wIG9mZnNldD0iMTAwJSIgc3R5bGU9InN0b3AtY29sb3I6I2ZmNjZmZjtzdG9wLW9wYWNpdHk6MC4xIi8+CjwvbGluZWFyR3JhZGllbnQ+CjwvZGVmcz4KPHBhdGggZD0iTSAwIDAgTCAwIDMwIE0gMCAwIEwgMzAgMCBNIDE1IDAgTCAxNSAzMCBNIDAgMTUgTCAzMCAxNSIgc3Ryb2tlPSJ1cmwoI2dyYWRHcmlkKSIgc3Ryb2tlLXdpZHRoPSIwLjUiLz4KPC9zdmc+Cg==',
    opacity: 0.8
  },
  // 浅色背景预设
  {
    name: '纯白',
    type: 'color',
    color: '#ffffff',
    imageUrl: '',
    opacity: 1.0
  },
  {
    name: '浅灰',
    type: 'color',
    color: '#f8f9fa',
    imageUrl: '',
    opacity: 1.0
  },
  {
    name: '米白',
    type: 'color',
    color: '#fefefe',
    imageUrl: '',
    opacity: 1.0
  },
  {
    name: '天空蓝',
    type: 'color',
    color: '#e3f2fd',
    imageUrl: '',
    opacity: 1.0
  },
  {
    name: '薄荷绿',
    type: 'color',
    color: '#e8f5e8',
    imageUrl: '',
    opacity: 1.0
  },
  {
    name: '淡紫',
    type: 'color',
    color: '#f3e5f5',
    imageUrl: '',
    opacity: 1.0
  },
  {
    name: '浅粉',
    type: 'color',
    color: '#fce4ec',
    imageUrl: '',
    opacity: 1.0
  },
  {
    name: '浅橙',
    type: 'color',
    color: '#fff3e0',
    imageUrl: '',
    opacity: 1.0
  },
  {
    name: '浅黄',
    type: 'color',
    color: '#fffde7',
    imageUrl: '',
    opacity: 1.0
  },
  {
    name: '彩虹网格',
    type: 'image',
    color: '#ffffff',
    imageUrl:
      'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjZmZmZmZmIi8+CjxkZWZzPgo8bGluZWFyR3JhZGllbnQgaWQ9InJhaW5ib3ciIHgxPSIwJSIgeTE9IjAlIiB4Mj0iMTAwJSIgeTI9IjEwMCUiPgo8c3RvcCBvZmZzZXQ9IjAlIiBzdHlsZT0ic3RvcC1jb2xvcjojZmY2YjM1O3N0b3Atb3BhY2l0eTowLjMiLz4KPHN0b3Agb2Zmc2V0PSIzMyUiIHN0eWxlPSJzdG9wLWNvbG9yOiNmZmQ3MDA7c3RvcC1vcGFjaXR5OjAuMyIvPgo8c3RvcCBvZmZzZXQ9IjY2JSIgc3R5bGU9InN0b3AtY29sb3I6IzUyYzQxYTtzdG9wLW9wYWNpdHk6MC4zIi8+CjxzdG9wIG9mZnNldD0iMTAwJSIgc3R5bGU9InN0b3AtY29sb3I6IzE4OTBmZjtzdG9wLW9wYWNpdHk6MC4zIi8+CjwvbGluZWFyR3JhZGllbnQ+CjwvZGVmcz4KPHBhdGggZD0iTSAwIDAgTCAwIDQwIE0gMCAwIEwgNDAgMCBNIDIwIDAgTCAyMCA0MCBNIDAGMJBMIDQWIDA0MCIgc3Ryb2tlPSJ1cmwoI3JhaW5ib3cpIiBzdHJva2Utd2lkdGg9IjAuNSIvPgo8L3N2Zz4K',
    opacity: 0.9
  },
  {
    name: '樱花',
    type: 'image',
    color: '#fff0f5',
    imageUrl:
      'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIHZpZXdCb3g9IjAgMCA1MCA1MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjUwIiBoZWlnaHQ9IjUwIiBmaWxsPSIjZmZmMGY1Ii8+CjxjaXJjbGUgY3g9IjEwIiBjeT0iMTAiIHI9IjMiIGZpbGw9IiNmZmI3Yzc"IG9wYWNpdHk9IjAuNiIvPgo8Y2lyY2xlIGN4PSI0MCIgY3k9IjE1IiByPSIyIiBmaWxsPSIjZmZiN2M3IiBvcGFjaXR5PSIwLjUiLz4KPGNpcmNsZSBjeD0iMjUiIGN5PSIzNSIgcj0iMi41IiBmaWxsPSIjZmZiN2M3IiBvcGFjaXR5PSIwLjciLz4KPGNpcmNsZSBjeD0iOCIgY3k9IjQwIiByPSIyIiBmaWxsPSIjZmZiN2M3IiBvcGFjaXR5PSIwLjQiLz4KPC9zdmc+Cg==',
    opacity: 0.9
  },
  {
    name: '云朵',
    type: 'image',
    color: '#f0f8ff',
    imageUrl:
      'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA2MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjYwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjZjBmOGZmIi8+CjxlbGxpcHNlIGN4PSIyMCIgY3k9IjE1IiByeD0iMTIiIHJ5PSI4IiBmaWxsPSIjZTNmMmZkIiBvcGFjaXR5PSIwLjciLz4KPGVsbGlwc2UgY3g9IjQ1IiBjeT0iMjUiIHJ4PSIxMCIgcnk9IjYiIGZpbGw9IiNlM2YyZmQiIG9wYWNpdHk9IjAuNiIvPgo8ZWxsaXBzZSBjeD0iMTAiIGN5PSIzMCIgcng9IjgiIHJ5PSI1IiBmaWxsPSIjZTNmMmZkIiBvcGFjaXR5PSIwLjUiLz4KPC9zdmc+Cg==',
    opacity: 0.8
  }
])

// SSH Agents 状态变更处理
const handleSshAgentsStatusChange = (checked) => {
  userConfig.value.sshAgentsStatus = checked ? 1 : 2
}

// 图片上传处理
const handleImageUpload = (file) => {
  const reader = new FileReader()
  reader.onload = (e) => {
    userConfig.value.terminalBackground.imageUrl = e.target.result
  }
  reader.readAsDataURL(file)
  return false // 阻止默认上传行为
}

// 背景预设相关函数
const isPresetActive = (preset) => {
  const bg = userConfig.value.terminalBackground
  return bg.type === preset.type && bg.color === preset.color && bg.imageUrl === preset.imageUrl
}

const getPresetStyle = (preset) => {
  if (preset.type === 'image') {
    return {
      backgroundImage: `url(${preset.imageUrl})`,
      backgroundColor: preset.color,
      backgroundSize: 'cover',
      backgroundRepeat: 'repeat'
    }
  } else if (preset.type === 'color') {
    return {
      backgroundColor: preset.color
    }
  } else {
    return {
      backgroundColor: '#1a1a1a'
    }
  }
}

const applyBackgroundPreset = async (preset) => {
  console.log('Applying background preset:', preset)
  userConfig.value.terminalBackground = {
    type: preset.type,
    color: preset.color,
    imageUrl: preset.imageUrl,
    opacity: preset.opacity
  }

  // 立即保存配置并通知终端组件
  await saveConfig()

  console.log('Background preset applied:', preset.name, 'Config:', userConfig.value.terminalBackground)
}

// 加载保存的配置
const loadSavedConfig = async () => {
  try {
    const config = await userConfigStore.getConfig()
    if (config) {
      userConfig.value = {
        fontSize: config.fontSize || 12,
        fontFamily: config.fontFamily || 'Menlo, Monaco, "Courier New", Consolas, Courier, monospace',
        scrollBack: config.scrollBack || 1000,
        cursorStyle: config.cursorStyle || 'block',
        middleMouseEvent: config.middleMouseEvent || 'paste',
        rightMouseEvent: config.rightMouseEvent || 'contextMenu',
        terminalType: config.terminalType || 'vt100',
        sshAgentsStatus: config.sshAgentsStatus || 2,
        // 确保terminalBackground字段正确初始化
        terminalBackground: config.terminalBackground || {
          type: 'default',
          color: '#1a1a1a',
          imageUrl: '',
          opacity: 1.0
        },
        // 确保themeColors字段正确初始化
        themeColors: config.themeColors || {
          primaryColor: '#1890ff',
          accentColor: '#52c41a',
          backgroundColor: '#141414',
          surfaceColor: '#1f1f1f',
          textColor: '#ffffff',
          borderColor: '#303030'
        }
      }
    }
  } catch (error) {
    console.error('Failed to load config:', error)
  }
}

// 保存配置
const saveConfig = async () => {
  try {
    await userConfigStore.saveConfig(userConfig.value)

    // 发送事件通知终端组件更新背景
    try {
      eventBus.emit('updateTerminalBackground', userConfig.value.terminalBackground)
      console.log('Terminal background update event sent')
    } catch (eventError) {
      console.warn('Failed to send terminal background update event:', eventError)
    }

    console.log('Terminal config saved successfully')
  } catch (error) {
    console.error('Failed to save terminal config:', error)
  }
}

// 监听配置变化并自动保存
watch(
  () => userConfig.value,
  async () => {
    await saveConfig()
  },
  { deep: true }
)

// 监听字体变化
watch(
  () => userConfig.value.fontFamily,
  (newFontFamily) => {
    try {
      eventBus.emit('updateTerminalFont', newFontFamily)
    } catch (error) {
      console.warn('Failed to send font update event:', error)
    }
  }
)

onMounted(async () => {
  console.log('Terminal settings component mounted')
  await loadSavedConfig()
})
</script>

<style scoped>
/* 使用与原组件相同的样式 */
.userInfo {
  height: 100%;
  overflow-y: auto;
}

.userInfo-container {
  background-color: var(--bg-color) !important;
  color: var(--text-color) !important;
}

.custom-form {
  color: var(--text-color) !important;
}

.custom-form :deep(.ant-form-item-label > label) {
  color: var(--text-color) !important;
}

.label-text {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color);
}

.user_my-ant-form-item {
  margin-bottom: 16px;
}

.user_my-ant-form-item :deep(.ant-form-item-label) {
  text-align: left;
  padding-right: 8px;
}

.user_my-ant-form-item :deep(.ant-form-item-control) {
  text-align: right;
}

.user_my-ant-form-item-content {
  background-color: var(--bg-color-secondary) !important;
  border-color: var(--border-color) !important;
  color: var(--text-color) !important;
}

.terminal-type-select,
.font-family-select,
.mouse-event-select {
  min-width: 150px;
}

.custom-radio-group :deep(.ant-radio-wrapper) {
  color: var(--text-color) !important;
}

.mouse-events-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.mouse-event-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.mouse-event-label {
  min-width: 100px;
  color: var(--text-color);
  font-size: 12px;
}

.mouse-event-select {
  flex: 1;
  max-width: 120px;
}

/* 终端背景设置样式 */
.terminal-background-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.background-type-row,
.background-color-row,
.background-image-row,
.background-opacity-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.background-label {
  min-width: 120px;
  color: var(--text-color);
  font-size: 12px;
}

.background-type-select {
  min-width: 150px;
}

.color-picker {
  width: 50px;
  height: 30px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  cursor: pointer;
}

.image-url-input {
  flex: 1;
  max-width: 200px;
}

.upload-button {
  margin-left: 8px;
}

.opacity-slider {
  flex: 1;
  max-width: 150px;
}

/* 背景预设样式 */
.background-presets-container {
  width: 100%;
}

.preset-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(75px, 1fr));
  gap: 10px;
  margin-top: 8px;
  max-height: 200px;
  overflow-y: auto;
}

.preset-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  padding: 8px;
  border: 2px solid transparent;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.preset-item:hover {
  border-color: var(--primary-color, #1890ff);
  background-color: var(--bg-color-secondary);
}

.preset-item.active {
  border-color: var(--primary-color, #1890ff);
  background-color: var(--bg-color-secondary);
}

.preset-preview {
  width: 45px;
  height: 28px;
  border-radius: 4px;
  border: 1px solid var(--border-color);
  background-size: cover;
  background-repeat: repeat;
  background-position: center;
}

.preset-name {
  font-size: 10px;
  color: var(--text-color);
  text-align: center;
  line-height: 1.2;
}
</style>
