/**
 * 设置迁移工具
 * 用于处理不同版本之间的配置迁移和升级
 */

import { sanitizeUserSettings, getDefaultSettings } from './settingsValidator'

export interface MigrationResult {
  success: boolean
  version: string
  changes: string[]
  errors: string[]
}

export interface LegacyConfig {
  [key: string]: any
}

/**
 * 当前配置版本
 */
export const CURRENT_CONFIG_VERSION = '1.2.0'

/**
 * 配置版本历史
 */
export const CONFIG_VERSION_HISTORY = [
  '1.0.0', // 初始版本
  '1.1.0', // 添加终端配置
  '1.2.0'  // 添加主题配置和数据同步
]

/**
 * 检测配置版本
 */
export function detectConfigVersion(config: any): string {
  // 如果有版本字段，直接返回
  if (config.version) {
    return config.version
  }

  // 根据配置字段推断版本
  if (config.themeColors && config.dataSync) {
    return '1.2.0'
  }
  
  if (config.terminalConfig || config.fontSize) {
    return '1.1.0'
  }
  
  return '1.0.0'
}

/**
 * 从 1.0.0 迁移到 1.1.0
 */
function migrateFrom1_0_0(config: LegacyConfig): { config: any, changes: string[] } {
  const changes: string[] = []
  const newConfig = { ...config }

  // 迁移终端相关配置
  if (config.fontSize || config.fontFamily || config.cursorStyle) {
    newConfig.terminalConfig = {
      terminalType: config.terminalType || 'xterm-256color',
      fontSize: config.fontSize || 14,
      fontFamily: config.fontFamily || 'Monaco, Menlo, "Ubuntu Mono", monospace',
      scrollBack: config.scrollBack || 1000,
      cursorStyle: config.cursorStyle || 'block',
      rightMouseEvent: config.rightMouseEvent || 'paste',
      middleMouseEvent: config.middleMouseEvent || 'paste',
      sshAgentsStatus: config.sshAgentsStatus || 1
    }

    // 删除旧的字段
    delete newConfig.fontSize
    delete newConfig.fontFamily
    delete newConfig.cursorStyle
    delete newConfig.scrollBack
    delete newConfig.terminalType
    delete newConfig.rightMouseEvent
    delete newConfig.middleMouseEvent
    delete newConfig.sshAgentsStatus

    changes.push('终端配置已迁移到 terminalConfig 对象')
  }

  newConfig.version = '1.1.0'
  changes.push('配置版本升级到 1.1.0')

  return { config: newConfig, changes }
}

/**
 * 从 1.1.0 迁移到 1.2.0
 */
function migrateFrom1_1_0(config: LegacyConfig): { config: any, changes: string[] } {
  const changes: string[] = []
  const newConfig = { ...config }

  // 添加数据同步配置
  if (!newConfig.dataSync) {
    newConfig.dataSync = 'disabled'
    changes.push('添加数据同步配置，默认为禁用')
  }

  // 添加主题颜色配置
  if (!newConfig.themeColors) {
    newConfig.themeColors = {
      primaryColor: '#1890ff',
      accentColor: '#52c41a',
      backgroundColor: '#141414',
      surfaceColor: '#1f1f1f',
      textColor: '#ffffff',
      borderColor: '#303030'
    }
    changes.push('添加主题颜色配置')
  }

  // 迁移旧的主题相关配置
  if (config.primaryColor || config.backgroundColor) {
    if (config.primaryColor) {
      newConfig.themeColors.primaryColor = config.primaryColor
      delete newConfig.primaryColor
    }
    if (config.backgroundColor) {
      newConfig.themeColors.backgroundColor = config.backgroundColor
      delete newConfig.backgroundColor
    }
    changes.push('旧的主题配置已迁移到 themeColors 对象')
  }

  newConfig.version = '1.2.0'
  changes.push('配置版本升级到 1.2.0')

  return { config: newConfig, changes }
}

/**
 * 执行配置迁移
 */
export function migrateConfig(config: LegacyConfig): MigrationResult {
  const errors: string[] = []
  let changes: string[] = []
  let currentConfig = { ...config }

  try {
    const currentVersion = detectConfigVersion(config)
    
    // 如果已经是最新版本，无需迁移
    if (currentVersion === CURRENT_CONFIG_VERSION) {
      return {
        success: true,
        version: currentVersion,
        changes: [],
        errors: []
      }
    }

    // 按版本顺序执行迁移
    const versionIndex = CONFIG_VERSION_HISTORY.indexOf(currentVersion)
    
    if (versionIndex === -1) {
      errors.push(`未知的配置版本: ${currentVersion}`)
      return {
        success: false,
        version: currentVersion,
        changes: [],
        errors
      }
    }

    // 从当前版本开始，逐步迁移到最新版本
    for (let i = versionIndex; i < CONFIG_VERSION_HISTORY.length - 1; i++) {
      const fromVersion = CONFIG_VERSION_HISTORY[i]
      const toVersion = CONFIG_VERSION_HISTORY[i + 1]

      switch (fromVersion) {
        case '1.0.0':
          const result1_0 = migrateFrom1_0_0(currentConfig)
          currentConfig = result1_0.config
          changes.push(...result1_0.changes)
          break

        case '1.1.0':
          const result1_1 = migrateFrom1_1_0(currentConfig)
          currentConfig = result1_1.config
          changes.push(...result1_1.changes)
          break

        default:
          errors.push(`不支持从版本 ${fromVersion} 迁移到 ${toVersion}`)
          break
      }
    }

    // 验证和清理迁移后的配置
    const sanitizedConfig = sanitizeUserSettings(currentConfig)

    return {
      success: errors.length === 0,
      version: CURRENT_CONFIG_VERSION,
      changes,
      errors
    }

  } catch (error) {
    errors.push(`迁移过程中发生错误: ${error instanceof Error ? error.message : '未知错误'}`)
    return {
      success: false,
      version: detectConfigVersion(config),
      changes,
      errors
    }
  }
}

/**
 * 检查是否需要迁移
 */
export function needsMigration(config: any): boolean {
  const currentVersion = detectConfigVersion(config)
  return currentVersion !== CURRENT_CONFIG_VERSION
}

/**
 * 创建配置备份
 */
export function createConfigBackup(config: any): string {
  const backup = {
    timestamp: new Date().toISOString(),
    version: detectConfigVersion(config),
    config: { ...config }
  }
  return JSON.stringify(backup, null, 2)
}

/**
 * 从备份恢复配置
 */
export function restoreFromBackup(backupData: string): any {
  try {
    const backup = JSON.parse(backupData)
    if (!backup.config) {
      throw new Error('无效的备份格式')
    }
    return backup.config
  } catch (error) {
    throw new Error(`恢复备份失败: ${error instanceof Error ? error.message : '未知错误'}`)
  }
}

/**
 * 合并默认配置
 * 确保所有必需的字段都存在
 */
export function mergeWithDefaults(config: any): any {
  const defaults = getDefaultSettings()
  
  function deepMerge(target: any, source: any): any {
    const result = { ...target }
    
    for (const key in source) {
      if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
        result[key] = deepMerge(result[key] || {}, source[key])
      } else if (result[key] === undefined) {
        result[key] = source[key]
      }
    }
    
    return result
  }
  
  return deepMerge(config, defaults)
}

/**
 * 获取迁移摘要
 */
export function getMigrationSummary(result: MigrationResult): string {
  if (!result.success) {
    return `迁移失败: ${result.errors.join(', ')}`
  }
  
  if (result.changes.length === 0) {
    return '配置已是最新版本，无需迁移'
  }
  
  return `配置已成功迁移到版本 ${result.version}:\n${result.changes.map(change => `• ${change}`).join('\n')}`
}
